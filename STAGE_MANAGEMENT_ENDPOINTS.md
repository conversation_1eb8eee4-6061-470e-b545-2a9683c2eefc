# Enhanced Stage Management Endpoints

## Summary of New Endpoints Implemented

Based on the stage management screens you showed me, I've implemented all the missing endpoints to support the complete admin functionality.

## 🎯 **New Endpoints Added**

### **1. Enhanced Stage Details**
- **GET** `/admin/stages/:id` - Get single stage with full details including course outlines, quizzes, and statistics

### **2. Stage Publishing**
- **PUT** `/admin/stages/:id/publish` - Publish/unpublish stages

### **3. Course Outline Management**
- **POST** `/admin/stages/:id/outlines` - Add new course outline to existing stage
- **PUT** `/admin/stages/:id/outlines/:outlineId` - Update course outline
- **DELETE** `/admin/stages/:id/outlines/:outlineId` - Delete course outline
- **PUT** `/admin/stages/:id/outlines/reorder` - Reorder course outlines

### **4. Quiz Management**
- **POST** `/admin/stages/:id/quizzes` - Add new quiz question to stage
- **PUT** `/admin/stages/:id/quizzes/:quizId` - Update quiz question
- **DELETE** `/admin/stages/:id/quizzes/:quizId` - Delete quiz question
- **PUT** `/admin/stages/:id/quizzes/reorder` - Reorder quiz questions

### **5. Enhanced Statistics**
- **GET** `/admin/stages` - Now returns enhanced statistics matching your UI:
  - Completions count
  - First-timers (quit count)
  - Total coins/points available
  - Course outline count
  - Quiz count
  - Level display
  - Updated timestamps

---

## 📊 **Enhanced GET /admin/stages Response**

The main stages list now returns data that matches your UI exactly:

```json
{
  "success": true,
  "message": "Stages retrieved successfully",
  "data": [
    {
      "id": "stage_id",
      "title": "Dominion by faith",
      "description": "Stage description",
      "preacher": "Rev. Biodun Fatoyinbo",
      "order": 1,
      "isActive": true,
      
      // Statistics for your table
      "completions": 1287,        // "First-timers" column
      "firstTimers": 10,          // "Quit No." column  
      "level": "Stage 1",         // "Level" column
      "coins": 12000,             // "Coins" column
      "outlineCount": 2,          // Number of course outlines
      "quizCount": 5,             // Number of quiz questions
      
      "createdBy": {
        "fullName": "Admin User",
        "username": "admin"
      },
      "updatedAt": "2025-03-19T00:00:00.000Z",
      
      // Full content for detailed management
      "courseOutlines": [...],
      "quizzes": [...]
    }
  ]
}
```

---

## 🔧 **API Endpoint Details**

### **Get Stage Details**
```http
GET /admin/stages/:id
Authorization: Bearer {admin_token}
```

**Response includes:**
- Complete stage information
- All course outlines with order
- All quiz questions with answers
- Statistics (completions, quit count, total points)
- Creator information

### **Publish/Unpublish Stage**
```http
PUT /admin/stages/:id/publish
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "isActive": true  // or false to unpublish
}
```

### **Add Course Outline**
```http
POST /admin/stages/:id/outlines
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "title": "Absolute dependence on God",
  "description": "Optional description",
  "videoUrl": "https://youtube.com/watch?v=example",
  "videoFile": "https://cloudinary.com/video.mp4",
  "order": 1
}
```

### **Add Quiz Question**
```http
POST /admin/stages/:id/quizzes
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "question": "What does it mean to depend on God?",
  "questionType": "MULTIPLE_CHOICE",
  "options": [
    "Getting your opinion from friends",
    "Trusting in God with your whole heart"
  ],
  "correctAnswers": [1],
  "points": 500,
  "explanation": "Optional explanation",
  "order": 1
}
```

### **Reorder Items**
```http
PUT /admin/stages/:id/outlines/reorder
PUT /admin/stages/:id/quizzes/reorder
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "items": [
    { "id": "item_id_1", "order": 1 },
    { "id": "item_id_2", "order": 2 },
    { "id": "item_id_3", "order": 3 }
  ]
}
```

---

## ✅ **Features Now Supported**

### **From Stage List Screen:**
- ✅ Display all stages with statistics
- ✅ Show completions, quit count, level, coins
- ✅ Show preacher names
- ✅ Show last updated dates
- ✅ Add new stage button functionality

### **From Stage Detail Screen:**
- ✅ View complete stage details
- ✅ Edit stage title, description, preacher
- ✅ Add/edit/delete course outlines
- ✅ Support both YouTube URLs and file uploads
- ✅ Add/edit/delete quiz questions
- ✅ Reorder course outlines and quizzes
- ✅ Publish/unpublish stages
- ✅ Create quiz with multiple choice options
- ✅ Mark correct answers
- ✅ Set points for each question

### **Enhanced Data Structure:**
- ✅ Proper course outline management
- ✅ Quiz question management with points
- ✅ Stage statistics calculation
- ✅ Order management for content
- ✅ Publishing status control

---

## 🔒 **Security & Validation**

All endpoints include:
- ✅ Admin authentication required
- ✅ Input validation with Zod schemas
- ✅ Proper error handling
- ✅ Database transaction safety
- ✅ Relationship integrity checks

---

## 📚 **Swagger Documentation**

All new endpoints are documented in Swagger with:
- Request/response schemas
- Example payloads
- Error responses
- Authentication requirements

Access at: `http://localhost:3000/api-docs`

---

## 🧪 **Testing**

I recommend testing these endpoints in this order:

1. **GET** `/admin/stages` - Verify enhanced statistics
2. **GET** `/admin/stages/:id` - Check detailed stage view
3. **POST** `/admin/stages/:id/outlines` - Add course outline
4. **POST** `/admin/stages/:id/quizzes` - Add quiz question
5. **PUT** `/admin/stages/:id/publish` - Test publishing
6. **PUT** `/admin/stages/:id/outlines/reorder` - Test reordering

---

## 🚀 **Ready for Frontend Integration**

Your frontend can now:
- Display the exact stage statistics shown in your UI
- Manage course outlines and quizzes individually
- Support drag-and-drop reordering
- Handle both YouTube and uploaded video content
- Publish/unpublish stages as needed
- Show real-time completion and quit statistics

All endpoints return consistent JSON responses with proper error handling and match the data structure needed for your admin interface.
