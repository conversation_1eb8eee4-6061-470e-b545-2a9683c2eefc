import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError, sendServerError } from '../utils/response.utils';
import { calculateQuizScoreSingleChoice } from '../utils/auth.utils';
import {
  awardPoints,
  getUserPointsSummary
} from '../utils/points.utils';
import {
  awardDiamondsForStage,
  getUserDiamondsSummary,
  calculateDiamondsFromStagePoints
} from '../utils/diamond.utils';
import { CompleteStageRequest } from '../schemas/stage.schema';
import { awardBadge } from './badge.controller';
import { updateUserStreak } from './streak.controller';

/**
 * Get all available stages for user
 */
export const getStages = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get user's progress
    const userProgress = await prisma.userProgress.findMany({
      where: { userId },
      select: {
        stageId: true,
        isCompleted: true,
        score: true,
        attempts: true,
      },
    });

    // Get all active stages
    const stages = await prisma.stage.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        description: true,
        preacher: true,
        order: true,
      },
      orderBy: { order: 'asc' },
    });

    // Determine which stages are unlocked
    const stagesWithProgress = stages.map((stage, index) => {
      const progress = userProgress.find(p => p.stageId === stage.id);

      // First stage is always unlocked
      let isUnlocked = index === 0;

      // Subsequent stages are unlocked if previous stage is completed with >= 60%
      if (index > 0) {
        const previousStage = stages[index - 1];
        const previousProgress = userProgress.find(p => p.stageId === previousStage.id);
        isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 50);
      }

      return {
        ...stage,
        isUnlocked,
        isCompleted: progress?.isCompleted || false,
        score: progress?.score || null,
        attempts: progress?.attempts || 0,
      };
    });

    return sendSuccess(res, 'Stages retrieved successfully', stagesWithProgress);
  } catch (error) {
    console.error('Get stages error:', error);
    return sendServerError(res, 'Failed to get stages');
  }
};

/**
 * Get specific stage details with quiz questions
 */
export const getStageById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;

    // Get stage with course outlines and quizzes
    const stage = await prisma.stage.findFirst({
      where: {
        id,
        isActive: true,
      },
      include: {
        courseOutlines: {
          select: {
            id: true,
            title: true,
            description: true,
            videoUrl: true,
            videoFile: true,
            order: true,
          },
          orderBy: { order: 'asc' },
        },
        quizzes: {
          select: {
            id: true,
            question: true,
            questionType: true,
            options: true,
            points: true,
            explanation: true,
            order: true,
          },
          orderBy: { order: 'asc' },
        },
      },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Check if stage is unlocked for user
    const allStages = await prisma.stage.findMany({
      where: { isActive: true },
      select: { id: true, order: true },
      orderBy: { order: 'asc' },
    });

    const stageIndex = allStages.findIndex(s => s.id === id);
    let isUnlocked = stageIndex === 0; // First stage is always unlocked

    if (stageIndex > 0) {
      const previousStage = allStages[stageIndex - 1];
      const previousProgress = await prisma.userProgress.findUnique({
        where: {
          userId_stageId: {
            userId,
            stageId: previousStage.id,
          },
        },
      });

      isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 60);
    }

    if (!isUnlocked) {
      return sendError(res, 'Stage is locked. Complete previous stages first.', 403);
    }

    // Get user's progress for this stage
    const userProgress = await prisma.userProgress.findUnique({
      where: {
        userId_stageId: {
          userId,
          stageId: id,
        },
      },
    });

    return sendSuccess(res, 'Stage retrieved successfully', {
      ...stage,
      userProgress: {
        isCompleted: userProgress?.isCompleted || false,
        score: userProgress?.score || null,
        attempts: userProgress?.attempts || 0,
      },
    });
  } catch (error) {
    console.error('Get stage by ID error:', error);
    return sendServerError(res, 'Failed to get stage');
  }
};

/**
 * Complete stage by submitting quiz answers
 */
export const completeStage = async (req: Request, res: Response) => {
  try {
    const { stageId, answers }: CompleteStageRequest = req.body;
    const userId = req.user!.id;

    // Verify stage exists and is active
    const stage = await prisma.stage.findFirst({
      where: {
        id: stageId,
        isActive: true,
      },
      include: {
        quizzes: {
          select: {
            id: true,
            correctAnswer: true,
            points: true,
          },
        },
      },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Check if stage is unlocked
    const allStages = await prisma.stage.findMany({
      where: { isActive: true },
      select: { id: true, order: true },
      orderBy: { order: 'asc' },
    });

    const stageIndex = allStages.findIndex(s => s.id === stageId);
    let isUnlocked = stageIndex === 0;

    if (stageIndex > 0) {
      const previousStage = allStages[stageIndex - 1];
      const previousProgress = await prisma.userProgress.findUnique({
        where: {
          userId_stageId: {
            userId,
            stageId: previousStage.id,
          },
        },
      });

      isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 50);
    }

    if (!isUnlocked) {
      return sendError(res, 'Stage is locked. Complete previous stages first.', 403);
    }

    // Validate all quiz questions are answered
    const quizIds = stage.quizzes.map(q => q.id);
    const answeredQuizIds = answers.map(a => a.questionId);

    const missingAnswers = quizIds.filter(id => !answeredQuizIds.includes(id));
    if (missingAnswers.length > 0) {
      return sendError(res, 'Please answer all quiz questions', 400);
    }

    // Calculate score and points for single choice questions
    const correctAnswers = stage.quizzes.map(quiz => ({
      questionId: quiz.id,
      correctAnswer: quiz.correctAnswer,
    }));

    const score = calculateQuizScoreSingleChoice(answers, correctAnswers);

    // Calculate points based on individual quiz points
    let pointsEarned = 0;
    for (const answer of answers) {
      const quiz = stage.quizzes.find(q => q.id === answer.questionId);
      if (quiz) {
        const correctAnswer = correctAnswers.find(ca => ca.questionId === answer.questionId);
        if (correctAnswer) {
          // Check if user's answer is correct (single choice)
          const isCorrect = answer.selectedAnswer === correctAnswer.correctAnswer;

          if (isCorrect) {
            pointsEarned += quiz.points;
          }
        }
      }
    }

    const isCompleted = score >= 50;

    // Get or create user progress
    const existingProgress = await prisma.userProgress.findUnique({
      where: {
        userId_stageId: {
          userId,
          stageId,
        },
      },
    });

    const progressData = {
      score,
      pointsEarned,
      attempts: (existingProgress?.attempts || 0) + 1,
      answers: answers,
      isCompleted,
      completedAt: isCompleted ? new Date() : null,
    };

    if (existingProgress) {
      await prisma.userProgress.update({
        where: { id: existingProgress.id },
        data: progressData,
      });
    } else {
      await prisma.userProgress.create({
        data: {
          userId,
          stageId,
          ...progressData,
        },
      });
    }

    // Award points if any were earned
    if (pointsEarned > 0) {
      await awardPoints(
        userId,
        pointsEarned,
        `Quiz completed: ${stage.title}`,
        stageId
      );
    }

    // If stage completed for the first time, award diamonds and create gift redemption record
    let awardedBadge = null;
    let diamondsEarned = 0;
    if (isCompleted && (!existingProgress || !existingProgress.isCompleted)) {
      // Award diamonds based on stage points
      const diamondResult = await awardDiamondsForStage(
        userId,
        stageId,
        stage.title,
        pointsEarned
      );
      diamondsEarned = diamondResult.diamonds;

      // Award badge for completing this stage
      awardedBadge = await awardBadge(userId, stageId);

      // Update user's streak
      await updateUserStreak(userId);
    }

    // Get diamonds summary
    const diamondsSummary = await getUserDiamondsSummary(userId);

    // Calculate potential diamonds for current score (for display during quiz)
    const potentialDiamonds = await calculateDiamondsFromStagePoints(pointsEarned);

    // Get updated points summary
    const pointsSummary = await getUserPointsSummary(userId);

    return sendSuccess(res, 'Quiz submitted successfully', {
      score,
      pointsEarned,
      isCompleted,
      attempts: progressData.attempts,
      diamondsEarned,
      potentialDiamonds,
      diamondsSummary,
      pointsSummary,
      badgeEarned: awardedBadge?.badge ? {
        id: awardedBadge.badge.id,
        name: awardedBadge.badge.name,
        description: awardedBadge.badge.description,
        icon: awardedBadge.badge.icon,
      } : null,
      message: isCompleted
        ? `Congratulations! You passed this stage and earned ${pointsEarned} points and ${diamondsEarned} diamond${diamondsEarned !== 1 ? 's' : ''}! Visit a PRU stand to claim your gift!`
        : `You earned ${pointsEarned} points! You need at least 50% to pass. Please try again.`,
    });
  } catch (error) {
    console.error('Complete stage error:', error);
    return sendServerError(res, 'Failed to complete stage');
  }
};

/**
 * Get user's overall progress
 */
export const getUserProgress = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get all stages and user progress
    const stages = await prisma.stage.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        order: true,
      },
      orderBy: { order: 'asc' },
    });

    const userProgress = await prisma.userProgress.findMany({
      where: { userId },
      select: {
        stageId: true,
        isCompleted: true,
        score: true,
        pointsEarned: true,
        attempts: true,
        completedAt: true,
      },
    });

    // Calculate overall statistics
    const totalStages = stages.length;
    const completedStages = userProgress.filter(p => p.isCompleted).length;
    const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;

    // Get gift redemptions
    const giftRedemptions = await prisma.giftRedemption.findMany({
      where: { userId },
      select: {
        id: true,
        milestone: true,
        pointsRequired: true,
        isRedeemed: true,
        redeemedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    const stagesWithProgress = stages.map(stage => {
      const progress = userProgress.find(p => p.stageId === stage.id);
      return {
        ...stage,
        isCompleted: progress?.isCompleted || false,
        score: progress?.score || null,
        pointsEarned: progress?.pointsEarned || 0,
        attempts: progress?.attempts || 0,
        completedAt: progress?.completedAt || null,
      };
    });

    // Get points summary
    const pointsSummary = await getUserPointsSummary(userId);

    return sendSuccess(res, 'Progress retrieved successfully', {
      totalStages,
      completedStages,
      progressPercentage,
      stages: stagesWithProgress,
      giftRedemptions,
      pointsSummary,
    });
  } catch (error) {
    console.error('Get user progress error:', error);
    return sendServerError(res, 'Failed to get progress');
  }
};

/**
 * Get user's points summary and transaction history
 */
export const getUserPoints = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pointsSummary = await getUserPointsSummary(userId);

    return sendSuccess(res, 'Points summary retrieved successfully', pointsSummary);
  } catch (error) {
    console.error('Get user points error:', error);
    return sendServerError(res, 'Failed to get points summary');
  }
};
