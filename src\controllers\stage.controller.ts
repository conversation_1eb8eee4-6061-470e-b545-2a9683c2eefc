import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError, sendServerError } from '../utils/response.utils';
import { calculateQuizScoreSingleChoice } from '../utils/auth.utils';
import {
  awardPoints,
  getUserPointsSummary
} from '../utils/points.utils';
import {
  awardDiamondsForStage,
  getUserDiamondsSummary,
  calculateDiamondsFromStagePoints
} from '../utils/diamond.utils';
import { CompleteStageRequest } from '../schemas/stage.schema';
import { awardBadge } from './badge.controller';
import { updateUserStreak } from './streak.controller';

/**
 * Get all available stages for user
 */
export const getStages = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get user's progress
    const userProgress = await prisma.userProgress.findMany({
      where: { userId },
      select: {
        stageId: true,
        isCompleted: true,
        score: true,
        attempts: true,
      },
    });

    // Get all active stages
    const stages = await prisma.stage.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        description: true,
        preacher: true,
        order: true,
      },
      orderBy: { order: 'asc' },
    });

    // Determine which stages are unlocked
    const stagesWithProgress = stages.map((stage, index) => {
      const progress = userProgress.find(p => p.stageId === stage.id);

      // First stage is always unlocked
      let isUnlocked = index === 0;

      // Subsequent stages are unlocked if previous stage is completed with >= 60%
      if (index > 0) {
        const previousStage = stages[index - 1];
        const previousProgress = userProgress.find(p => p.stageId === previousStage.id);
        isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 50);
      }

      return {
        ...stage,
        isUnlocked,
        isCompleted: progress?.isCompleted || false,
        score: progress?.score || null,
        attempts: progress?.attempts || 0,
      };
    });

    return sendSuccess(res, 'Stages retrieved successfully', stagesWithProgress);
  } catch (error) {
    console.error('Get stages error:', error);
    return sendServerError(res, 'Failed to get stages');
  }
};

/**
 * Get specific stage details with quiz questions
 */
export const getStageById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;

    // Get stage with course outlines and quizzes
    const stage = await prisma.stage.findFirst({
      where: {
        id,
        isActive: true,
      },
      include: {
        courseOutlines: {
          select: {
            id: true,
            title: true,
            description: true,
            videoUrl: true,
            videoFile: true,
            duration: true,
            order: true,
          },
          orderBy: { order: 'asc' },
        },
        quizzes: {
          select: {
            id: true,
            question: true,
            questionType: true,
            options: true,
            points: true,
            explanation: true,
            order: true,
          },
          orderBy: { order: 'asc' },
        },
      },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Check if stage is unlocked for user
    const allStages = await prisma.stage.findMany({
      where: { isActive: true },
      select: { id: true, order: true },
      orderBy: { order: 'asc' },
    });

    const stageIndex = allStages.findIndex(s => s.id === id);
    let isUnlocked = stageIndex === 0; // First stage is always unlocked

    if (stageIndex > 0) {
      const previousStage = allStages[stageIndex - 1];
      const previousProgress = await prisma.userProgress.findUnique({
        where: {
          userId_stageId: {
            userId,
            stageId: previousStage.id,
          },
        },
      });

      isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 60);
    }

    if (!isUnlocked) {
      return sendError(res, 'Stage is locked. Complete previous stages first.', 403);
    }

    // Get user's progress for this stage
    const userProgress = await prisma.userProgress.findUnique({
      where: {
        userId_stageId: {
          userId,
          stageId: id,
        },
      },
    });

    // Get lesson progress for each course outline
    const lessonsWithProgress = await Promise.all(
      stage.courseOutlines.map(async (outline) => {
        const lessonProgress = await prisma.lessonProgress.findUnique({
          where: {
            userId_courseOutlineId: {
              userId,
              courseOutlineId: outline.id,
            },
          },
        });

        return {
          ...outline,
          progress: lessonProgress ? {
            isCompleted: lessonProgress.isCompleted,
            watchTime: lessonProgress.watchTime,
            totalDuration: lessonProgress.totalDuration,
            completedAt: lessonProgress.completedAt,
          } : null,
        };
      })
    );

    const completedLessons = lessonsWithProgress.filter(lesson =>
      lesson.progress?.isCompleted
    ).length;

    return sendSuccess(res, 'Stage retrieved successfully', {
      ...stage,
      lessons: lessonsWithProgress, // Rename for frontend clarity
      totalLessons: stage.courseOutlines.length,
      completedLessons,
      userProgress: {
        isCompleted: userProgress?.isCompleted || false,
        score: userProgress?.score || null,
        attempts: userProgress?.attempts || 0,
        pointsEarned: userProgress?.pointsEarned || 0,
      },
    });
  } catch (error) {
    console.error('Get stage by ID error:', error);
    return sendServerError(res, 'Failed to get stage');
  }
};

/**
 * Complete stage by submitting quiz answers
 */
export const completeStage = async (req: Request, res: Response) => {
  try {
    const { stageId, answers }: CompleteStageRequest = req.body;
    const userId = req.user!.id;

    // Verify stage exists and is active
    const stage = await prisma.stage.findFirst({
      where: {
        id: stageId,
        isActive: true,
      },
      include: {
        quizzes: {
          select: {
            id: true,
            correctAnswer: true,
            points: true,
          },
        },
      },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Check if stage is unlocked
    const allStages = await prisma.stage.findMany({
      where: { isActive: true },
      select: { id: true, order: true },
      orderBy: { order: 'asc' },
    });

    const stageIndex = allStages.findIndex(s => s.id === stageId);
    let isUnlocked = stageIndex === 0;

    if (stageIndex > 0) {
      const previousStage = allStages[stageIndex - 1];
      const previousProgress = await prisma.userProgress.findUnique({
        where: {
          userId_stageId: {
            userId,
            stageId: previousStage.id,
          },
        },
      });

      isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 50);
    }

    if (!isUnlocked) {
      return sendError(res, 'Stage is locked. Complete previous stages first.', 403);
    }

    // Validate all quiz questions are answered
    const quizIds = stage.quizzes.map(q => q.id);
    const answeredQuizIds = answers.map(a => a.questionId);

    const missingAnswers = quizIds.filter(id => !answeredQuizIds.includes(id));
    if (missingAnswers.length > 0) {
      return sendError(res, 'Please answer all quiz questions', 400);
    }

    // Calculate score and points for single choice questions
    const correctAnswers = stage.quizzes.map(quiz => ({
      questionId: quiz.id,
      correctAnswer: quiz.correctAnswer,
    }));

    const score = calculateQuizScoreSingleChoice(answers, correctAnswers);

    // Calculate points based on individual quiz points
    let pointsEarned = 0;
    for (const answer of answers) {
      const quiz = stage.quizzes.find(q => q.id === answer.questionId);
      if (quiz) {
        const correctAnswer = correctAnswers.find(ca => ca.questionId === answer.questionId);
        if (correctAnswer) {
          // Check if user's answer is correct (single choice)
          const isCorrect = answer.selectedAnswer === correctAnswer.correctAnswer;

          if (isCorrect) {
            pointsEarned += quiz.points;
          }
        }
      }
    }

    const isCompleted = score >= 50;

    // Get or create user progress
    const existingProgress = await prisma.userProgress.findUnique({
      where: {
        userId_stageId: {
          userId,
          stageId,
        },
      },
    });

    const progressData = {
      score,
      pointsEarned,
      attempts: (existingProgress?.attempts || 0) + 1,
      answers: answers,
      isCompleted,
      completedAt: isCompleted ? new Date() : null,
    };

    if (existingProgress) {
      await prisma.userProgress.update({
        where: { id: existingProgress.id },
        data: progressData,
      });
    } else {
      await prisma.userProgress.create({
        data: {
          userId,
          stageId,
          ...progressData,
        },
      });
    }

    // Award points if any were earned
    if (pointsEarned > 0) {
      await awardPoints(
        userId,
        pointsEarned,
        `Quiz completed: ${stage.title}`,
        stageId
      );
    }

    // If stage completed for the first time, award diamonds and create gift redemption record
    let awardedBadge = null;
    let diamondsEarned = 0;
    if (isCompleted && (!existingProgress || !existingProgress.isCompleted)) {
      // Award diamonds based on stage points
      const diamondResult = await awardDiamondsForStage(
        userId,
        stageId,
        stage.title,
        pointsEarned
      );
      diamondsEarned = diamondResult.diamonds;

      // Award badge for completing this stage
      awardedBadge = await awardBadge(userId, stageId);

      // Update user's streak
      await updateUserStreak(userId);
    }

    // Get diamonds summary
    const diamondsSummary = await getUserDiamondsSummary(userId);

    // Calculate potential diamonds for current score (for display during quiz)
    const potentialDiamonds = await calculateDiamondsFromStagePoints(pointsEarned);

    // Get updated points summary
    const pointsSummary = await getUserPointsSummary(userId);

    return sendSuccess(res, 'Quiz submitted successfully', {
      score,
      pointsEarned,
      isCompleted,
      attempts: progressData.attempts,
      diamondsEarned,
      potentialDiamonds,
      diamondsSummary,
      pointsSummary,
      badgeEarned: awardedBadge?.badge ? {
        id: awardedBadge.badge.id,
        name: awardedBadge.badge.name,
        description: awardedBadge.badge.description,
        icon: awardedBadge.badge.icon,
      } : null,
      message: isCompleted
        ? `Congratulations! You passed this stage and earned ${pointsEarned} points and ${diamondsEarned} diamond${diamondsEarned !== 1 ? 's' : ''}! Visit a PRU stand to claim your gift!`
        : `You earned ${pointsEarned} points! You need at least 50% to pass. Please try again.`,
    });
  } catch (error) {
    console.error('Complete stage error:', error);
    return sendServerError(res, 'Failed to complete stage');
  }
};

/**
 * Get user's overall progress
 */
export const getUserProgress = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get all stages and user progress
    const stages = await prisma.stage.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        order: true,
      },
      orderBy: { order: 'asc' },
    });

    const userProgress = await prisma.userProgress.findMany({
      where: { userId },
      select: {
        stageId: true,
        isCompleted: true,
        score: true,
        pointsEarned: true,
        attempts: true,
        completedAt: true,
      },
    });

    // Calculate overall statistics
    const totalStages = stages.length;
    const completedStages = userProgress.filter(p => p.isCompleted).length;
    const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;

    // Get gift redemptions
    const giftRedemptions = await prisma.giftRedemption.findMany({
      where: { userId },
      select: {
        id: true,
        milestone: true,
        pointsRequired: true,
        isRedeemed: true,
        redeemedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get lesson progress for each stage to calculate individual progress percentages
    const stagesWithProgress = await Promise.all(stages.map(async (stage) => {
      const progress = userProgress.find(p => p.stageId === stage.id);

      // Get lesson progress for this stage
      const courseOutlines = await prisma.courseOutline.findMany({
        where: { stageId: stage.id },
        include: {
          lessonProgress: {
            where: { userId },
          },
        },
      });

      // Calculate lesson-based progress percentage
      const totalLessons = courseOutlines.length;
      const completedLessons = courseOutlines.filter(
        outline => outline.lessonProgress[0]?.isCompleted
      ).length;
      const lessonProgressPercentage = totalLessons > 0
        ? Math.round((completedLessons / totalLessons) * 100)
        : 0;

      // Calculate quiz-based progress percentage (if quiz is completed)
      const quizProgressPercentage = progress?.isCompleted ? 100 : 0;

      // Overall stage progress is the average of lesson and quiz progress
      // But if quiz is completed, stage is considered 100% complete
      const overallProgressPercentage = progress?.isCompleted
        ? 100
        : Math.round((lessonProgressPercentage + quizProgressPercentage) / 2);

      return {
        ...stage,
        isCompleted: progress?.isCompleted || false,
        score: progress?.score || null,
        pointsEarned: progress?.pointsEarned || 0,
        attempts: progress?.attempts || 0,
        completedAt: progress?.completedAt || null,
        progressPercentage: overallProgressPercentage,
        lessonProgress: {
          totalLessons,
          completedLessons,
          lessonProgressPercentage,
        },
        quizProgress: {
          isCompleted: progress?.isCompleted || false,
          score: progress?.score || null,
          quizProgressPercentage,
        },
      };
    }));

    // Get points summary
    const pointsSummary = await getUserPointsSummary(userId);

    // Get diamonds summary
    const diamondsSummary = await getUserDiamondsSummary(userId);

    // Calculate overall progress statistics
    const totalProgressPercentage = stagesWithProgress.length > 0
      ? Math.round(stagesWithProgress.reduce((sum, stage) => sum + stage.progressPercentage, 0) / stagesWithProgress.length)
      : 0;

    return sendSuccess(res, 'Progress retrieved successfully', {
      totalStages,
      completedStages,
      progressPercentage, // Overall completion percentage (completed stages / total stages)
      totalProgressPercentage, // Average progress percentage across all stages
      stages: stagesWithProgress,
      giftRedemptions,
      pointsSummary,
      diamondsSummary,
    });
  } catch (error) {
    console.error('Get user progress error:', error);
    return sendServerError(res, 'Failed to get progress');
  }
};

/**
 * Get user's points summary and transaction history
 */
export const getUserPoints = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pointsSummary = await getUserPointsSummary(userId);

    return sendSuccess(res, 'Points summary retrieved successfully', pointsSummary);
  } catch (error) {
    console.error('Get user points error:', error);
    return sendServerError(res, 'Failed to get points summary');
  }
};

/**
 * Get individual lesson by ID
 */
export const getLessonById = async (req: Request, res: Response) => {
  try {
    const { stageId, lessonId } = req.params;
    const userId = req.user!.id;

    const lesson = await prisma.courseOutline.findFirst({
      where: {
        id: lessonId,
        stageId: stageId,
        stage: { isActive: true }
      },
      include: {
        lessonProgress: {
          where: { userId }
        },
        stage: {
          select: {
            id: true,
            title: true,
            preacher: true
          }
        }
      }
    });

    if (!lesson) {
      return sendError(res, 'Lesson not found', 404);
    }

    return sendSuccess(res, 'Lesson retrieved successfully', {
      id: lesson.id,
      title: lesson.title,
      description: lesson.description,
      videoUrl: lesson.videoUrl,
      videoFile: lesson.videoFile,
      duration: lesson.duration,
      order: lesson.order,
      stage: lesson.stage,
      progress: lesson.lessonProgress[0] || null
    });
  } catch (error) {
    console.error('Get lesson error:', error);
    return sendServerError(res, 'Failed to get lesson');
  }
};

/**
 * Get next lesson in sequence
 */
export const getNextLesson = async (req: Request, res: Response) => {
  try {
    const { stageId, lessonId } = req.params;

    const currentLesson = await prisma.courseOutline.findFirst({
      where: { id: lessonId, stageId },
      select: { order: true }
    });

    if (!currentLesson) {
      return sendError(res, 'Current lesson not found', 404);
    }

    const nextLesson = await prisma.courseOutline.findFirst({
      where: {
        stageId,
        order: { gt: currentLesson.order }
      },
      orderBy: { order: 'asc' }
    });

    if (!nextLesson) {
      // Check if there's a next stage
      const currentStage = await prisma.stage.findUnique({
        where: { id: stageId },
        select: { order: true }
      });

      const nextStage = await prisma.stage.findFirst({
        where: {
          order: { gt: currentStage!.order },
          isActive: true
        },
        orderBy: { order: 'asc' },
        include: {
          courseOutlines: {
            orderBy: { order: 'asc' },
            take: 1
          }
        }
      });

      return sendSuccess(res, 'Navigation info retrieved', {
        nextLesson: null,
        nextStage: nextStage ? {
          id: nextStage.id,
          title: nextStage.title,
          firstLesson: nextStage.courseOutlines[0] || null
        } : null
      });
    }

    return sendSuccess(res, 'Next lesson found', {
      nextLesson,
      nextStage: null
    });
  } catch (error) {
    console.error('Get next lesson error:', error);
    return sendServerError(res, 'Failed to get next lesson');
  }
};

/**
 * Get previous lesson in sequence
 */
export const getPreviousLesson = async (req: Request, res: Response) => {
  try {
    const { stageId, lessonId } = req.params;

    const currentLesson = await prisma.courseOutline.findFirst({
      where: { id: lessonId, stageId },
      select: { order: true }
    });

    if (!currentLesson) {
      return sendError(res, 'Current lesson not found', 404);
    }

    const previousLesson = await prisma.courseOutline.findFirst({
      where: {
        stageId,
        order: { lt: currentLesson.order }
      },
      orderBy: { order: 'desc' }
    });

    if (!previousLesson) {
      // Check if there's a previous stage
      const currentStage = await prisma.stage.findUnique({
        where: { id: stageId },
        select: { order: true }
      });

      const previousStage = await prisma.stage.findFirst({
        where: {
          order: { lt: currentStage!.order },
          isActive: true
        },
        orderBy: { order: 'desc' },
        include: {
          courseOutlines: {
            orderBy: { order: 'desc' },
            take: 1
          }
        }
      });

      return sendSuccess(res, 'Navigation info retrieved', {
        previousLesson: null,
        previousStage: previousStage ? {
          id: previousStage.id,
          title: previousStage.title,
          lastLesson: previousStage.courseOutlines[0] || null
        } : null
      });
    }

    return sendSuccess(res, 'Previous lesson found', {
      previousLesson,
      previousStage: null
    });
  } catch (error) {
    console.error('Get previous lesson error:', error);
    return sendServerError(res, 'Failed to get previous lesson');
  }
};
