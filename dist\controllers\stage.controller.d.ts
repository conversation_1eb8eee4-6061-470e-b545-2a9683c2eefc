import { Request, Response } from 'express';
export declare const getStages: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getStageById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const completeStage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUserProgress: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUserPoints: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=stage.controller.d.ts.map