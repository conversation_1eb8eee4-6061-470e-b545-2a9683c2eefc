import { z } from 'zod';

// Update streak schema
export const updateStreakSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
});

// Get streak schema
export const getStreakSchema = z.object({
  userId: z.string().optional(), // For admin to view specific user's streak
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format').optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
});

export type UpdateStreakInput = z.infer<typeof updateStreakSchema>;
export type GetStreakInput = z.infer<typeof getStreakSchema>;
