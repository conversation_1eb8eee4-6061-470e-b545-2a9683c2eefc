"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileUploadSchema = exports.initiateGiftRedemptionSchema = exports.verifyGiftRedemptionSchema = exports.searchUserByPhoneSchema = exports.updateReportSchema = exports.createReportSchema = exports.assignBuddySchema = exports.createAdminSchema = exports.adminLoginSchema = void 0;
const zod_1 = require("zod");
exports.adminLoginSchema = zod_1.z.object({
    username: zod_1.z.string().min(3, 'Username must be at least 3 characters'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters'),
});
exports.createAdminSchema = zod_1.z.object({
    username: zod_1.z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username too long'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters'),
    fullName: zod_1.z.string().min(2, 'Full name must be at least 2 characters').max(100, 'Full name too long'),
    role: zod_1.z.enum(['SUPER_ADMIN', 'ADMIN', 'MODERATOR']).default('ADMIN'),
});
exports.assignBuddySchema = zod_1.z.object({
    userId: zod_1.z.string().min(1, 'User ID is required'),
    buddyId: zod_1.z.string().min(1, 'Buddy ID is required'),
});
exports.createReportSchema = zod_1.z.object({
    userId: zod_1.z.string().min(1, 'User ID is required'),
    title: zod_1.z.string().min(1, 'Report title is required').max(200, 'Title too long'),
    content: zod_1.z.string().min(1, 'Report content is required'),
    status: zod_1.z.enum(['ACTIVE', 'NEEDS_HELP', 'COMPLETED', 'PAUSED']).default('ACTIVE'),
    tags: zod_1.z.array(zod_1.z.string()).default([]),
});
exports.updateReportSchema = exports.createReportSchema.partial().omit({ userId: true });
exports.searchUserByPhoneSchema = zod_1.z.object({
    phone: zod_1.z.string().regex(/^(\+234|234|0)?[789][01]\d{8}$/, 'Invalid phone number'),
});
exports.verifyGiftRedemptionSchema = zod_1.z.object({
    phone: zod_1.z.string().regex(/^(\+234|234|0)?[789][01]\d{8}$/, 'Invalid phone number'),
    userCode: zod_1.z.string().min(1, 'User code is required'),
});
exports.initiateGiftRedemptionSchema = zod_1.z.object({
    userId: zod_1.z.string().min(1, 'User ID is required'),
    pointsToRedeem: zod_1.z.number().int().min(1000, 'Minimum 1000 points required for redemption'),
    pin: zod_1.z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
});
exports.fileUploadSchema = zod_1.z.object({
    file: zod_1.z.any(),
    folder: zod_1.z.string().optional().default('coza-connect'),
});
//# sourceMappingURL=admin.schema.js.map