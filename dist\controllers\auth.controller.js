"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfile = exports.forgotPin = exports.login = exports.register = exports.verifyOtp = exports.sendOtp = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const auth_utils_1 = require("../utils/auth.utils");
const otp_utils_1 = require("../utils/otp.utils");
const auth_middleware_1 = require("../middleware/auth.middleware");
const sendOtp = async (req, res) => {
    try {
        const { phone } = req.body;
        const normalizedPhone = (0, auth_utils_1.normalizePhoneNumber)(phone);
        const { otp, success } = await otp_utils_1.OTPService.generateAndSendOTP(normalizedPhone);
        if (!success) {
            return (0, response_utils_1.sendError)(res, 'Failed to send OTP. Please try again.', 500);
        }
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
        await db_1.default.oTPVerification.create({
            data: {
                phone: normalizedPhone,
                otp,
                expiresAt,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'OTP sent successfully', {
            phone: normalizedPhone,
            expiresIn: 600,
        });
    }
    catch (error) {
        console.error('Send OTP error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to send OTP');
    }
};
exports.sendOtp = sendOtp;
const verifyOtp = async (req, res) => {
    try {
        const { phone, otp } = req.body;
        const normalizedPhone = (0, auth_utils_1.normalizePhoneNumber)(phone);
        const otpRecord = await db_1.default.oTPVerification.findFirst({
            where: {
                phone: normalizedPhone,
                otp,
                isUsed: false,
                expiresAt: {
                    gt: new Date(),
                },
            },
        });
        if (!otpRecord) {
            return (0, response_utils_1.sendError)(res, 'Invalid or expired OTP', 400);
        }
        await db_1.default.oTPVerification.update({
            where: { id: otpRecord.id },
            data: { isUsed: true },
        });
        return (0, response_utils_1.sendSuccess)(res, 'OTP verified successfully', {
            phone: normalizedPhone,
            verified: true,
        });
    }
    catch (error) {
        console.error('Verify OTP error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to verify OTP');
    }
};
exports.verifyOtp = verifyOtp;
const register = async (req, res) => {
    try {
        const { phone, fullName, pin } = req.body;
        const normalizedPhone = (0, auth_utils_1.normalizePhoneNumber)(phone);
        const recentVerification = await db_1.default.oTPVerification.findFirst({
            where: {
                phone: normalizedPhone,
                isUsed: true,
                expiresAt: {
                    gt: new Date(Date.now() - 30 * 60 * 1000),
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        if (!recentVerification) {
            return (0, response_utils_1.sendError)(res, 'Phone number not verified. Please verify your phone number first.', 400);
        }
        const existingUser = await db_1.default.user.findUnique({
            where: { phone: normalizedPhone },
        });
        if (existingUser) {
            return (0, response_utils_1.sendError)(res, 'User with this phone number already exists', 409);
        }
        const hashedPin = await (0, auth_utils_1.hashPassword)(pin);
        const nameParts = fullName.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';
        const user = await db_1.default.user.create({
            data: {
                phone: normalizedPhone,
                fullName: fullName.trim(),
                firstName,
                lastName,
                pin: hashedPin,
            },
            select: {
                id: true,
                phone: true,
                fullName: true,
                firstName: true,
                lastName: true,
                points: true,
                createdAt: true,
            },
        });
        const token = (0, auth_middleware_1.generateUserToken)(user);
        return (0, response_utils_1.sendSuccess)(res, 'Registration successful', {
            user,
            token,
        }, 201);
    }
    catch (error) {
        console.error('Registration error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Registration failed');
    }
};
exports.register = register;
const login = async (req, res) => {
    try {
        const { phone, pin } = req.body;
        const normalizedPhone = (0, auth_utils_1.normalizePhoneNumber)(phone);
        const user = await db_1.default.user.findUnique({
            where: { phone: normalizedPhone },
            select: {
                id: true,
                phone: true,
                fullName: true,
                pin: true,
                isActive: true,
            },
        });
        if (!user || !user.isActive) {
            return (0, response_utils_1.sendError)(res, 'Invalid phone number or PIN', 401);
        }
        const isPinValid = await (0, auth_utils_1.comparePassword)(pin, user.pin);
        if (!isPinValid) {
            return (0, response_utils_1.sendError)(res, 'Invalid phone number or PIN', 401);
        }
        const token = (0, auth_middleware_1.generateUserToken)({
            id: user.id,
            phone: user.phone,
            fullName: user.fullName,
        });
        return (0, response_utils_1.sendSuccess)(res, 'Login successful', {
            user: {
                id: user.id,
                phone: user.phone,
                fullName: user.fullName,
            },
            token,
        });
    }
    catch (error) {
        console.error('Login error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Login failed');
    }
};
exports.login = login;
const forgotPin = async (req, res) => {
    try {
        const { phone, otp, newPin } = req.body;
        const normalizedPhone = (0, auth_utils_1.normalizePhoneNumber)(phone);
        const otpRecord = await db_1.default.oTPVerification.findFirst({
            where: {
                phone: normalizedPhone,
                otp,
                isUsed: false,
                expiresAt: {
                    gt: new Date(),
                },
            },
        });
        if (!otpRecord) {
            return (0, response_utils_1.sendError)(res, 'Invalid or expired OTP', 400);
        }
        const user = await db_1.default.user.findUnique({
            where: { phone: normalizedPhone },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        const hashedPin = await (0, auth_utils_1.hashPassword)(newPin);
        await db_1.default.user.update({
            where: { id: user.id },
            data: { pin: hashedPin },
        });
        await db_1.default.oTPVerification.update({
            where: { id: otpRecord.id },
            data: { isUsed: true },
        });
        return (0, response_utils_1.sendSuccess)(res, 'PIN reset successful');
    }
    catch (error) {
        console.error('Forgot PIN error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to reset PIN');
    }
};
exports.forgotPin = forgotPin;
const getProfile = async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await db_1.default.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                phone: true,
                fullName: true,
                firstName: true,
                lastName: true,
                gender: true,
                church: true,
                profileImage: true,
                points: true,
                createdAt: true,
                buddy: {
                    select: {
                        id: true,
                        fullName: true,
                        phone: true,
                    },
                },
                progress: {
                    include: {
                        stage: {
                            select: {
                                id: true,
                                title: true,
                                order: true,
                            },
                        },
                    },
                    orderBy: {
                        stage: {
                            order: 'asc',
                        },
                    },
                },
            },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        return (0, response_utils_1.sendSuccess)(res, 'Profile retrieved successfully', user);
    }
    catch (error) {
        console.error('Get profile error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get profile');
    }
};
exports.getProfile = getProfile;
//# sourceMappingURL=auth.controller.js.map