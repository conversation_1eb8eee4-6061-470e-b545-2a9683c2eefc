"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendServerError = exports.sendValidationError = exports.sendError = exports.sendSuccess = void 0;
const sendSuccess = (res, message, data, statusCode = 200) => {
    const response = {
        success: true,
        message,
        data,
    };
    return res.status(statusCode).json(response);
};
exports.sendSuccess = sendSuccess;
const sendError = (res, message, statusCode = 400, error) => {
    const response = {
        success: false,
        message,
        error,
    };
    return res.status(statusCode).json(response);
};
exports.sendError = sendError;
const sendValidationError = (res, message = 'Validation failed', errors, statusCode = 400) => {
    const response = {
        success: false,
        message,
        errors,
    };
    return res.status(statusCode).json(response);
};
exports.sendValidationError = sendValidationError;
const sendServerError = (res, message = 'Internal server error', error) => {
    console.error('Server Error:', error);
    const response = {
        success: false,
        message,
        ...(process.env.NODE_ENV === 'development' && { error }),
    };
    return res.status(500).json(response);
};
exports.sendServerError = sendServerError;
//# sourceMappingURL=response.utils.js.map