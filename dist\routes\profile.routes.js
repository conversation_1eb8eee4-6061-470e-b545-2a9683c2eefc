"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const profile_controller_1 = require("../controllers/profile.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const profile_schema_1 = require("../schemas/profile.schema");
const cloudinary_config_1 = require("../config/cloudinary.config");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authenticateUser);
router.get('/', (0, validation_middleware_1.validateQuery)(profile_schema_1.getProfileSchema), profile_controller_1.getProfile);
router.put('/', (0, validation_middleware_1.validateBody)(profile_schema_1.updateProfileSchema), profile_controller_1.updateProfile);
router.post('/change-pin', (0, validation_middleware_1.validateBody)(profile_schema_1.changePinSchema), profile_controller_1.changePin);
router.post('/upload-image', cloudinary_config_1.upload.single('profileImage'), profile_controller_1.uploadProfileImage);
router.delete('/image', profile_controller_1.deleteProfileImage);
exports.default = router;
//# sourceMappingURL=profile.routes.js.map