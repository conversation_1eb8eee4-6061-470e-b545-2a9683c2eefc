"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserPoints = exports.getUserProgress = exports.completeStage = exports.getStageById = exports.getStages = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const auth_utils_1 = require("../utils/auth.utils");
const points_utils_1 = require("../utils/points.utils");
const badge_controller_1 = require("./badge.controller");
const streak_controller_1 = require("./streak.controller");
const getStages = async (req, res) => {
    try {
        const userId = req.user.id;
        const userProgress = await db_1.default.userProgress.findMany({
            where: { userId },
            select: {
                stageId: true,
                isCompleted: true,
                score: true,
                attempts: true,
            },
        });
        const stages = await db_1.default.stage.findMany({
            where: { isActive: true },
            select: {
                id: true,
                title: true,
                description: true,
                preacher: true,
                order: true,
            },
            orderBy: { order: 'asc' },
        });
        const stagesWithProgress = stages.map((stage, index) => {
            const progress = userProgress.find(p => p.stageId === stage.id);
            let isUnlocked = index === 0;
            if (index > 0) {
                const previousStage = stages[index - 1];
                const previousProgress = userProgress.find(p => p.stageId === previousStage.id);
                isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 60);
            }
            return {
                ...stage,
                isUnlocked,
                isCompleted: progress?.isCompleted || false,
                score: progress?.score || null,
                attempts: progress?.attempts || 0,
            };
        });
        return (0, response_utils_1.sendSuccess)(res, 'Stages retrieved successfully', stagesWithProgress);
    }
    catch (error) {
        console.error('Get stages error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get stages');
    }
};
exports.getStages = getStages;
const getStageById = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        const stage = await db_1.default.stage.findFirst({
            where: {
                id,
                isActive: true,
            },
            include: {
                courseOutlines: {
                    select: {
                        id: true,
                        title: true,
                        description: true,
                        videoUrl: true,
                        videoFile: true,
                        order: true,
                    },
                    orderBy: { order: 'asc' },
                },
                quizzes: {
                    select: {
                        id: true,
                        question: true,
                        questionType: true,
                        options: true,
                        points: true,
                        explanation: true,
                        order: true,
                    },
                    orderBy: { order: 'asc' },
                },
            },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const allStages = await db_1.default.stage.findMany({
            where: { isActive: true },
            select: { id: true, order: true },
            orderBy: { order: 'asc' },
        });
        const stageIndex = allStages.findIndex(s => s.id === id);
        let isUnlocked = stageIndex === 0;
        if (stageIndex > 0) {
            const previousStage = allStages[stageIndex - 1];
            const previousProgress = await db_1.default.userProgress.findUnique({
                where: {
                    userId_stageId: {
                        userId,
                        stageId: previousStage.id,
                    },
                },
            });
            isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 60);
        }
        if (!isUnlocked) {
            return (0, response_utils_1.sendError)(res, 'Stage is locked. Complete previous stages first.', 403);
        }
        const userProgress = await db_1.default.userProgress.findUnique({
            where: {
                userId_stageId: {
                    userId,
                    stageId: id,
                },
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Stage retrieved successfully', {
            ...stage,
            userProgress: {
                isCompleted: userProgress?.isCompleted || false,
                score: userProgress?.score || null,
                attempts: userProgress?.attempts || 0,
            },
        });
    }
    catch (error) {
        console.error('Get stage by ID error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get stage');
    }
};
exports.getStageById = getStageById;
const completeStage = async (req, res) => {
    try {
        const { stageId, answers } = req.body;
        const userId = req.user.id;
        const stage = await db_1.default.stage.findFirst({
            where: {
                id: stageId,
                isActive: true,
            },
            include: {
                quizzes: {
                    select: {
                        id: true,
                        correctAnswers: true,
                        points: true,
                    },
                },
            },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const allStages = await db_1.default.stage.findMany({
            where: { isActive: true },
            select: { id: true, order: true },
            orderBy: { order: 'asc' },
        });
        const stageIndex = allStages.findIndex(s => s.id === stageId);
        let isUnlocked = stageIndex === 0;
        if (stageIndex > 0) {
            const previousStage = allStages[stageIndex - 1];
            const previousProgress = await db_1.default.userProgress.findUnique({
                where: {
                    userId_stageId: {
                        userId,
                        stageId: previousStage.id,
                    },
                },
            });
            isUnlocked = Boolean(previousProgress?.isCompleted && (previousProgress?.score || 0) >= 60);
        }
        if (!isUnlocked) {
            return (0, response_utils_1.sendError)(res, 'Stage is locked. Complete previous stages first.', 403);
        }
        const quizIds = stage.quizzes.map(q => q.id);
        const answeredQuizIds = answers.map(a => a.questionId);
        const missingAnswers = quizIds.filter(id => !answeredQuizIds.includes(id));
        if (missingAnswers.length > 0) {
            return (0, response_utils_1.sendError)(res, 'Please answer all quiz questions', 400);
        }
        const correctAnswers = stage.quizzes.map(quiz => ({
            questionId: quiz.id,
            correctAnswers: quiz.correctAnswers,
        }));
        const score = (0, auth_utils_1.calculateQuizScore)(answers, correctAnswers);
        let pointsEarned = 0;
        for (const answer of answers) {
            const quiz = stage.quizzes.find(q => q.id === answer.questionId);
            if (quiz) {
                const correctAnswer = correctAnswers.find(ca => ca.questionId === answer.questionId);
                if (correctAnswer) {
                    const isCorrect = answer.selectedAnswers.length === correctAnswer.correctAnswers.length &&
                        answer.selectedAnswers.every(ans => correctAnswer.correctAnswers.includes(ans));
                    if (isCorrect) {
                        pointsEarned += quiz.points;
                    }
                }
            }
        }
        const isCompleted = score >= 60;
        const existingProgress = await db_1.default.userProgress.findUnique({
            where: {
                userId_stageId: {
                    userId,
                    stageId,
                },
            },
        });
        const progressData = {
            score,
            pointsEarned,
            attempts: (existingProgress?.attempts || 0) + 1,
            answers: answers,
            isCompleted,
            completedAt: isCompleted ? new Date() : null,
        };
        if (existingProgress) {
            await db_1.default.userProgress.update({
                where: { id: existingProgress.id },
                data: progressData,
            });
        }
        else {
            await db_1.default.userProgress.create({
                data: {
                    userId,
                    stageId,
                    ...progressData,
                },
            });
        }
        if (pointsEarned > 0) {
            await (0, points_utils_1.awardPoints)(userId, pointsEarned, `Quiz completed: ${stage.title}`, stageId);
        }
        let awardedBadge = null;
        if (isCompleted && (!existingProgress || !existingProgress.isCompleted)) {
            const userCode = (0, auth_utils_1.generateUserCode)();
            await db_1.default.giftRedemption.create({
                data: {
                    userId,
                    adminId: '000000000000000000000000',
                    milestone: `${stage.title} Completed - Code: ${userCode}`,
                },
            });
            awardedBadge = await (0, badge_controller_1.awardBadge)(userId, stageId);
            await (0, streak_controller_1.updateUserStreak)(userId);
        }
        const newPointGifts = await (0, points_utils_1.checkAndCreatePointGifts)(userId);
        const pointsSummary = await (0, points_utils_1.getUserPointsSummary)(userId);
        return (0, response_utils_1.sendSuccess)(res, 'Quiz submitted successfully', {
            score,
            pointsEarned,
            isCompleted,
            attempts: progressData.attempts,
            pointsSummary,
            newGiftsEarned: newPointGifts.length,
            badgeEarned: awardedBadge?.badge ? {
                id: awardedBadge.badge.id,
                name: awardedBadge.badge.name,
                description: awardedBadge.badge.description,
                icon: awardedBadge.badge.icon,
            } : null,
            message: isCompleted
                ? `Congratulations! You passed this stage and earned ${pointsEarned} points! Visit a PRU stand to claim your gift!`
                : `You earned ${pointsEarned} points! You need at least 60% to pass. Please try again.`,
        });
    }
    catch (error) {
        console.error('Complete stage error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to complete stage');
    }
};
exports.completeStage = completeStage;
const getUserProgress = async (req, res) => {
    try {
        const userId = req.user.id;
        const stages = await db_1.default.stage.findMany({
            where: { isActive: true },
            select: {
                id: true,
                title: true,
                order: true,
            },
            orderBy: { order: 'asc' },
        });
        const userProgress = await db_1.default.userProgress.findMany({
            where: { userId },
            select: {
                stageId: true,
                isCompleted: true,
                score: true,
                pointsEarned: true,
                attempts: true,
                completedAt: true,
            },
        });
        const totalStages = stages.length;
        const completedStages = userProgress.filter(p => p.isCompleted).length;
        const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;
        const giftRedemptions = await db_1.default.giftRedemption.findMany({
            where: { userId },
            select: {
                id: true,
                milestone: true,
                pointsRequired: true,
                isRedeemed: true,
                redeemedAt: true,
            },
            orderBy: { createdAt: 'desc' },
        });
        const stagesWithProgress = stages.map(stage => {
            const progress = userProgress.find(p => p.stageId === stage.id);
            return {
                ...stage,
                isCompleted: progress?.isCompleted || false,
                score: progress?.score || null,
                pointsEarned: progress?.pointsEarned || 0,
                attempts: progress?.attempts || 0,
                completedAt: progress?.completedAt || null,
            };
        });
        const pointsSummary = await (0, points_utils_1.getUserPointsSummary)(userId);
        return (0, response_utils_1.sendSuccess)(res, 'Progress retrieved successfully', {
            totalStages,
            completedStages,
            progressPercentage,
            stages: stagesWithProgress,
            giftRedemptions,
            pointsSummary,
        });
    }
    catch (error) {
        console.error('Get user progress error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get progress');
    }
};
exports.getUserProgress = getUserProgress;
const getUserPoints = async (req, res) => {
    try {
        const userId = req.user.id;
        const pointsSummary = await (0, points_utils_1.getUserPointsSummary)(userId);
        return (0, response_utils_1.sendSuccess)(res, 'Points summary retrieved successfully', pointsSummary);
    }
    catch (error) {
        console.error('Get user points error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get points summary');
    }
};
exports.getUserPoints = getUserPoints;
//# sourceMappingURL=stage.controller.js.map