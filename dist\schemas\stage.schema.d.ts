import { z } from 'zod';
export declare const quizAnswerSchema: z.ZodObject<{
    questionId: z.ZodString;
    selectedAnswers: z.<PERSON><z.ZodNumber, "many">;
}, "strip", z.ZodTypeAny, {
    questionId: string;
    selectedAnswers: number[];
}, {
    questionId: string;
    selectedAnswers: number[];
}>;
export declare const completeStageSchema: z.ZodObject<{
    stageId: z.ZodString;
    answers: z.Zod<PERSON>rray<z.ZodObject<{
        questionId: z.ZodString;
        selectedAnswers: z.<PERSON><PERSON><PERSON><z.ZodNumber, "many">;
    }, "strip", z.ZodType<PERSON>ny, {
        questionId: string;
        selectedAnswers: number[];
    }, {
        questionId: string;
        selectedAnswers: number[];
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    stageId: string;
    answers: {
        questionId: string;
        selectedAnswers: number[];
    }[];
}, {
    stageId: string;
    answers: {
        questionId: string;
        selectedAnswers: number[];
    }[];
}>;
export declare const courseOutlineSchema: z.ZodObject<{
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    videoUrl: z.ZodOptional<z.ZodString>;
    videoFile: z.ZodOptional<z.ZodString>;
    order: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    order: number;
    title: string;
    description?: string | undefined;
    videoUrl?: string | undefined;
    videoFile?: string | undefined;
}, {
    order: number;
    title: string;
    description?: string | undefined;
    videoUrl?: string | undefined;
    videoFile?: string | undefined;
}>;
export declare const quizSchema: z.ZodObject<{
    question: z.ZodString;
    questionType: z.ZodDefault<z.ZodEnum<["MULTIPLE_CHOICE", "TRUE_FALSE", "SINGLE_CHOICE"]>>;
    options: z.ZodArray<z.ZodString, "many">;
    correctAnswers: z.ZodArray<z.ZodNumber, "many">;
    points: z.ZodDefault<z.ZodNumber>;
    explanation: z.ZodOptional<z.ZodString>;
    order: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    order: number;
    points: number;
    options: string[];
    question: string;
    questionType: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE";
    correctAnswers: number[];
    explanation?: string | undefined;
}, {
    order: number;
    options: string[];
    question: string;
    correctAnswers: number[];
    points?: number | undefined;
    questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
    explanation?: string | undefined;
}>;
export declare const createStageSchema: z.ZodObject<{
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    preacher: z.ZodOptional<z.ZodString>;
    order: z.ZodNumber;
    courseOutlines: z.ZodArray<z.ZodObject<{
        title: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        videoUrl: z.ZodOptional<z.ZodString>;
        videoFile: z.ZodOptional<z.ZodString>;
        order: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }, {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }>, "many">;
    quizzes: z.ZodArray<z.ZodObject<{
        question: z.ZodString;
        questionType: z.ZodDefault<z.ZodEnum<["MULTIPLE_CHOICE", "TRUE_FALSE", "SINGLE_CHOICE"]>>;
        options: z.ZodArray<z.ZodString, "many">;
        correctAnswers: z.ZodArray<z.ZodNumber, "many">;
        points: z.ZodDefault<z.ZodNumber>;
        explanation: z.ZodOptional<z.ZodString>;
        order: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        order: number;
        points: number;
        options: string[];
        question: string;
        questionType: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE";
        correctAnswers: number[];
        explanation?: string | undefined;
    }, {
        order: number;
        options: string[];
        question: string;
        correctAnswers: number[];
        points?: number | undefined;
        questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
        explanation?: string | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    order: number;
    title: string;
    courseOutlines: {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }[];
    quizzes: {
        order: number;
        points: number;
        options: string[];
        question: string;
        questionType: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE";
        correctAnswers: number[];
        explanation?: string | undefined;
    }[];
    description?: string | undefined;
    preacher?: string | undefined;
}, {
    order: number;
    title: string;
    courseOutlines: {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }[];
    quizzes: {
        order: number;
        options: string[];
        question: string;
        correctAnswers: number[];
        points?: number | undefined;
        questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
        explanation?: string | undefined;
    }[];
    description?: string | undefined;
    preacher?: string | undefined;
}>;
export declare const updateStageSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    preacher: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    order: z.ZodOptional<z.ZodNumber>;
    courseOutlines: z.ZodOptional<z.ZodArray<z.ZodObject<{
        title: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        videoUrl: z.ZodOptional<z.ZodString>;
        videoFile: z.ZodOptional<z.ZodString>;
        order: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }, {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }>, "many">>;
    quizzes: z.ZodOptional<z.ZodArray<z.ZodObject<{
        question: z.ZodString;
        questionType: z.ZodDefault<z.ZodEnum<["MULTIPLE_CHOICE", "TRUE_FALSE", "SINGLE_CHOICE"]>>;
        options: z.ZodArray<z.ZodString, "many">;
        correctAnswers: z.ZodArray<z.ZodNumber, "many">;
        points: z.ZodDefault<z.ZodNumber>;
        explanation: z.ZodOptional<z.ZodString>;
        order: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        order: number;
        points: number;
        options: string[];
        question: string;
        questionType: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE";
        correctAnswers: number[];
        explanation?: string | undefined;
    }, {
        order: number;
        options: string[];
        question: string;
        correctAnswers: number[];
        points?: number | undefined;
        questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
        explanation?: string | undefined;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    order?: number | undefined;
    title?: string | undefined;
    description?: string | undefined;
    preacher?: string | undefined;
    courseOutlines?: {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }[] | undefined;
    quizzes?: {
        order: number;
        points: number;
        options: string[];
        question: string;
        questionType: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE";
        correctAnswers: number[];
        explanation?: string | undefined;
    }[] | undefined;
}, {
    order?: number | undefined;
    title?: string | undefined;
    description?: string | undefined;
    preacher?: string | undefined;
    courseOutlines?: {
        order: number;
        title: string;
        description?: string | undefined;
        videoUrl?: string | undefined;
        videoFile?: string | undefined;
    }[] | undefined;
    quizzes?: {
        order: number;
        options: string[];
        question: string;
        correctAnswers: number[];
        points?: number | undefined;
        questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
        explanation?: string | undefined;
    }[] | undefined;
}>;
export declare const createCourseOutlineSchema: z.ZodObject<{
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    videoUrl: z.ZodOptional<z.ZodString>;
    videoFile: z.ZodOptional<z.ZodString>;
    order: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    order: number;
    title: string;
    description?: string | undefined;
    videoUrl?: string | undefined;
    videoFile?: string | undefined;
}, {
    order: number;
    title: string;
    description?: string | undefined;
    videoUrl?: string | undefined;
    videoFile?: string | undefined;
}>;
export declare const updateCourseOutlineSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    videoUrl: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    videoFile: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    order: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    order?: number | undefined;
    title?: string | undefined;
    description?: string | undefined;
    videoUrl?: string | undefined;
    videoFile?: string | undefined;
}, {
    order?: number | undefined;
    title?: string | undefined;
    description?: string | undefined;
    videoUrl?: string | undefined;
    videoFile?: string | undefined;
}>;
export declare const createQuizSchema: z.ZodObject<{
    question: z.ZodString;
    questionType: z.ZodDefault<z.ZodEnum<["MULTIPLE_CHOICE", "TRUE_FALSE", "SINGLE_CHOICE"]>>;
    options: z.ZodArray<z.ZodString, "many">;
    correctAnswers: z.ZodArray<z.ZodNumber, "many">;
    points: z.ZodDefault<z.ZodNumber>;
    explanation: z.ZodOptional<z.ZodString>;
    order: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    order: number;
    points: number;
    options: string[];
    question: string;
    questionType: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE";
    correctAnswers: number[];
    explanation?: string | undefined;
}, {
    order: number;
    options: string[];
    question: string;
    correctAnswers: number[];
    points?: number | undefined;
    questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
    explanation?: string | undefined;
}>;
export declare const updateQuizSchema: z.ZodObject<{
    question: z.ZodOptional<z.ZodString>;
    questionType: z.ZodOptional<z.ZodDefault<z.ZodEnum<["MULTIPLE_CHOICE", "TRUE_FALSE", "SINGLE_CHOICE"]>>>;
    options: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    correctAnswers: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    points: z.ZodOptional<z.ZodDefault<z.ZodNumber>>;
    explanation: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    order: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    order?: number | undefined;
    points?: number | undefined;
    options?: string[] | undefined;
    question?: string | undefined;
    questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
    correctAnswers?: number[] | undefined;
    explanation?: string | undefined;
}, {
    order?: number | undefined;
    points?: number | undefined;
    options?: string[] | undefined;
    question?: string | undefined;
    questionType?: "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SINGLE_CHOICE" | undefined;
    correctAnswers?: number[] | undefined;
    explanation?: string | undefined;
}>;
export declare const publishStageSchema: z.ZodObject<{
    isActive: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    isActive: boolean;
}, {
    isActive: boolean;
}>;
export declare const reorderItemsSchema: z.ZodObject<{
    items: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        order: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        id: string;
        order: number;
    }, {
        id: string;
        order: number;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    items: {
        id: string;
        order: number;
    }[];
}, {
    items: {
        id: string;
        order: number;
    }[];
}>;
export type QuizAnswer = z.infer<typeof quizAnswerSchema>;
export type CompleteStageRequest = z.infer<typeof completeStageSchema>;
export type CourseOutline = z.infer<typeof courseOutlineSchema>;
export type Quiz = z.infer<typeof quizSchema>;
export type CreateStageRequest = z.infer<typeof createStageSchema>;
export type UpdateStageRequest = z.infer<typeof updateStageSchema>;
export type CreateCourseOutlineRequest = z.infer<typeof createCourseOutlineSchema>;
export type UpdateCourseOutlineRequest = z.infer<typeof updateCourseOutlineSchema>;
export type CreateQuizRequest = z.infer<typeof createQuizSchema>;
export type UpdateQuizRequest = z.infer<typeof updateQuizSchema>;
export type PublishStageRequest = z.infer<typeof publishStageSchema>;
export type ReorderItemsRequest = z.infer<typeof reorderItemsSchema>;
//# sourceMappingURL=stage.schema.d.ts.map