"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStreakLeaderboard = exports.manualUpdateStreak = exports.updateUserStreak = exports.getUserStreak = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const streak_schema_1 = require("../schemas/streak.schema");
const getUserStreak = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        let userStreak = await db_1.default.userStreak.findUnique({
            where: { userId },
        });
        if (!userStreak) {
            userStreak = await db_1.default.userStreak.create({
                data: {
                    userId,
                    currentStreak: 0,
                    longestStreak: 0,
                    streakData: {},
                },
            });
        }
        const streakData = userStreak.streakData || {};
        const today = new Date();
        const last7Days = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            last7Days.push({
                date: dateStr,
                day: date.toLocaleDateString('en-US', { weekday: 'short' }).charAt(0),
                isActive: streakData[dateStr] || false,
            });
        }
        const response = {
            currentStreak: userStreak.currentStreak,
            longestStreak: userStreak.longestStreak,
            lastActiveDate: userStreak.lastActiveDate,
            last7Days,
            totalActiveDays: Object.values(streakData).filter(Boolean).length,
        };
        return (0, response_utils_1.sendSuccess)(res, 'User streak retrieved successfully', response);
    }
    catch (error) {
        console.error('Get user streak error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve user streak');
    }
};
exports.getUserStreak = getUserStreak;
const updateUserStreak = async (userId, activityDate) => {
    try {
        const dateStr = activityDate || new Date().toISOString().split('T')[0];
        let userStreak = await db_1.default.userStreak.findUnique({
            where: { userId },
        });
        if (!userStreak) {
            userStreak = await db_1.default.userStreak.create({
                data: {
                    userId,
                    currentStreak: 0,
                    longestStreak: 0,
                    streakData: {},
                },
            });
        }
        const streakData = userStreak.streakData || {};
        if (streakData[dateStr]) {
            return userStreak;
        }
        streakData[dateStr] = true;
        let currentStreak = 0;
        const today = new Date(dateStr);
        for (let i = 0; i >= -365; i--) {
            const checkDate = new Date(today);
            checkDate.setDate(checkDate.getDate() + i);
            const checkDateStr = checkDate.toISOString().split('T')[0];
            if (streakData[checkDateStr]) {
                currentStreak++;
            }
            else {
                break;
            }
        }
        const longestStreak = Math.max(userStreak.longestStreak, currentStreak);
        const updatedStreak = await db_1.default.userStreak.update({
            where: { userId },
            data: {
                currentStreak,
                longestStreak,
                lastActiveDate: new Date(dateStr),
                streakData,
            },
        });
        console.log(`Streak updated for user ${userId}: ${currentStreak} days`);
        return updatedStreak;
    }
    catch (error) {
        console.error('Update user streak error:', error);
        return null;
    }
};
exports.updateUserStreak = updateUserStreak;
const manualUpdateStreak = async (req, res) => {
    try {
        const validatedData = streak_schema_1.updateStreakSchema.parse(req.body);
        const { date } = validatedData;
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const updatedStreak = await (0, exports.updateUserStreak)(userId, date);
        if (!updatedStreak) {
            return (0, response_utils_1.sendError)(res, 'Failed to update streak');
        }
        return (0, response_utils_1.sendSuccess)(res, 'Streak updated successfully', {
            currentStreak: updatedStreak.currentStreak,
            longestStreak: updatedStreak.longestStreak,
            lastActiveDate: updatedStreak.lastActiveDate,
        });
    }
    catch (error) {
        console.error('Manual update streak error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to update streak');
    }
};
exports.manualUpdateStreak = manualUpdateStreak;
const getStreakLeaderboard = async (req, res) => {
    try {
        const topStreaks = await db_1.default.userStreak.findMany({
            include: {
                user: {
                    select: {
                        id: true,
                        fullName: true,
                        phone: true,
                    },
                },
            },
            orderBy: [
                { currentStreak: 'desc' },
                { longestStreak: 'desc' },
            ],
            take: 50,
        });
        const leaderboard = topStreaks.map((streak, index) => ({
            rank: index + 1,
            user: streak.user,
            currentStreak: streak.currentStreak,
            longestStreak: streak.longestStreak,
            lastActiveDate: streak.lastActiveDate,
            totalActiveDays: Object.values(streak.streakData || {}).filter(Boolean).length,
        }));
        return (0, response_utils_1.sendSuccess)(res, 'Streak leaderboard retrieved successfully', leaderboard);
    }
    catch (error) {
        console.error('Get streak leaderboard error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve streak leaderboard');
    }
};
exports.getStreakLeaderboard = getStreakLeaderboard;
//# sourceMappingURL=streak.controller.js.map