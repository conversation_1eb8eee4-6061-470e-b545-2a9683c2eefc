import { z } from 'zod';
export declare const updateProfileSchema: z.ZodObject<{
    firstName: z.ZodOptional<z.ZodString>;
    lastName: z.ZodOptional<z.ZodString>;
    gender: z.ZodOptional<z.Zod<PERSON>num<["Male", "Female", "Other"]>>;
    church: z.<PERSON>ption<PERSON><z.ZodString>;
    profileImage: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodType<PERSON>ny, {
    firstName?: string | undefined;
    lastName?: string | undefined;
    gender?: "Male" | "Female" | "Other" | undefined;
    church?: string | undefined;
    profileImage?: string | undefined;
}, {
    firstName?: string | undefined;
    lastName?: string | undefined;
    gender?: "Male" | "Female" | "Other" | undefined;
    church?: string | undefined;
    profileImage?: string | undefined;
}>;
export declare const changePinSchema: z.Zod<PERSON>cts<z.ZodObject<{
    currentPin: z.ZodString;
    newPin: z.ZodString;
    confirmPin: z.ZodString;
}, "strip", z.ZodTypeAny, {
    currentPin: string;
    newPin: string;
    confirmPin: string;
}, {
    currentPin: string;
    newPin: string;
    confirmPin: string;
}>, {
    currentPin: string;
    newPin: string;
    confirmPin: string;
}, {
    currentPin: string;
    newPin: string;
    confirmPin: string;
}>;
export declare const getProfileSchema: z.ZodObject<{
    includeProgress: z.ZodOptional<z.ZodBoolean>;
    includeBadges: z.ZodOptional<z.ZodBoolean>;
    includeStreak: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    includeProgress?: boolean | undefined;
    includeBadges?: boolean | undefined;
    includeStreak?: boolean | undefined;
}, {
    includeProgress?: boolean | undefined;
    includeBadges?: boolean | undefined;
    includeStreak?: boolean | undefined;
}>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type ChangePinInput = z.infer<typeof changePinSchema>;
export type GetProfileInput = z.infer<typeof getProfileSchema>;
//# sourceMappingURL=profile.schema.d.ts.map