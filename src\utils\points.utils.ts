import prisma from '../db/db';

/**
 * Points system constants
 */
export const POINTS_CONFIG = {
  POINTS_PER_CORRECT_ANSWER: 1000,
  FIRST_GIFT_THRESHOLD: 5000,
  SUBSEQUENT_GIFT_INTERVAL: 50000,
} as const;

/**
 * Calculate points earned from quiz answers
 */
export const calculatePointsFromQuiz = (
  userAnswers: Array<{ questionId: string; selectedAnswers: number[] }>,
  correctAnswers: Array<{ questionId: string; correctAnswers: number[] }>
): number => {
  if (userAnswers.length === 0 || correctAnswers.length === 0) {
    return 0;
  }

  let correctCount = 0;
  
  userAnswers.forEach(userAnswer => {
    const correctAnswer = correctAnswers.find(ca => ca.questionId === userAnswer.questionId);
    if (correctAnswer) {
      // Check if user's answers match correct answers exactly
      const userSet = new Set(userAnswer.selectedAnswers.sort());
      const correctSet = new Set(correctAnswer.correctAnswers.sort());
      
      if (userSet.size === correctSet.size && 
          [...userSet].every(answer => correctSet.has(answer))) {
        correctCount++;
      }
    }
  });

  return correctCount * POINTS_CONFIG.POINTS_PER_CORRECT_ANSWER;
};

/**
 * Calculate how many gifts a user is eligible for based on their points
 */
export const calculateEligibleGifts = (totalPoints: number): number => {
  if (totalPoints < POINTS_CONFIG.FIRST_GIFT_THRESHOLD) {
    return 0;
  }

  // First gift at 5000 points
  let gifts = 1;
  let remainingPoints = totalPoints - POINTS_CONFIG.FIRST_GIFT_THRESHOLD;

  // Additional gifts every 50000 points
  gifts += Math.floor(remainingPoints / POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL);

  return gifts;
};

/**
 * Calculate points required for next gift
 */
export const calculatePointsForNextGift = (totalPoints: number): number => {
  if (totalPoints < POINTS_CONFIG.FIRST_GIFT_THRESHOLD) {
    return POINTS_CONFIG.FIRST_GIFT_THRESHOLD - totalPoints;
  }

  const pointsAfterFirstGift = totalPoints - POINTS_CONFIG.FIRST_GIFT_THRESHOLD;
  const remainderPoints = pointsAfterFirstGift % POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL;
  
  return POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL - remainderPoints;
};

/**
 * Award points to user and create transaction record
 */
export const awardPoints = async (
  userId: string,
  points: number,
  description: string,
  stageId?: string
) => {
  return await prisma.$transaction(async (tx) => {
    // Update user's total points
    const updatedUser = await tx.user.update({
      where: { id: userId },
      data: {
        points: {
          increment: points,
        },
      },
    });

    // Create points transaction record
    await tx.pointsTransaction.create({
      data: {
        userId,
        type: 'EARNED',
        points,
        description,
        stageId,
      },
    });

    return updatedUser;
  });
};

/**
 * Deduct points from user and create transaction record
 */
export const deductPoints = async (
  userId: string,
  points: number,
  description: string,
  giftRedemptionId?: string
) => {
  return await prisma.$transaction(async (tx) => {
    // Check if user has enough points
    const user = await tx.user.findUnique({
      where: { id: userId },
      select: { points: true },
    });

    if (!user || user.points < points) {
      throw new Error('Insufficient points');
    }

    // Update user's total points
    const updatedUser = await tx.user.update({
      where: { id: userId },
      data: {
        points: {
          decrement: points,
        },
      },
    });

    // Create points transaction record
    await tx.pointsTransaction.create({
      data: {
        userId,
        type: 'DEDUCTED',
        points: -points, // Negative value to indicate deduction
        description,
        giftRedemptionId,
      },
    });

    return updatedUser;
  });
};

/**
 * Check and create point-based gift redemptions for user
 */
export const checkAndCreatePointGifts = async (userId: string) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { points: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  const eligibleGifts = calculateEligibleGifts(user.points);
  
  if (eligibleGifts === 0) {
    return [];
  }

  // Check how many point-based gifts user already has
  const existingPointGifts = await prisma.giftRedemption.count({
    where: {
      userId,
      pointsRequired: {
        not: null,
      },
    },
  });

  const newGiftsNeeded = eligibleGifts - existingPointGifts;
  
  if (newGiftsNeeded <= 0) {
    return [];
  }

  // Create new gift redemption records
  const newGifts = [];
  for (let i = 0; i < newGiftsNeeded; i++) {
    const giftNumber = existingPointGifts + i + 1;
    const pointsRequired = giftNumber === 1 
      ? POINTS_CONFIG.FIRST_GIFT_THRESHOLD 
      : POINTS_CONFIG.FIRST_GIFT_THRESHOLD + ((giftNumber - 1) * POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL);

    const gift = await prisma.giftRedemption.create({
      data: {
        userId,
        adminId: '000000000000000000000000', // Placeholder
        milestone: `${pointsRequired} Points Reached`,
        giftType: 'Points Achievement Gift',
        userCode: generateGiftCode(),
        pointsRequired,
        pointsDeducted: pointsRequired,
      },
    });

    newGifts.push(gift);
  }

  return newGifts;
};

/**
 * Generate unique gift code
 */
const generateGiftCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Get user's points summary
 */
export const getUserPointsSummary = async (userId: string) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { points: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  const eligibleGifts = calculateEligibleGifts(user.points);
  const pointsForNextGift = calculatePointsForNextGift(user.points);

  // Get recent points transactions
  const recentTransactions = await prisma.pointsTransaction.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: 10,
    select: {
      type: true,
      points: true,
      description: true,
      createdAt: true,
    },
  });

  return {
    totalPoints: user.points,
    eligibleGifts,
    pointsForNextGift,
    recentTransactions,
  };
};
