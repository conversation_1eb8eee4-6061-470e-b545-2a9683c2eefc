"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkUpdateLessonProgressSchema = exports.getLessonProgressSchema = exports.updateLessonProgressSchema = void 0;
const zod_1 = require("zod");
exports.updateLessonProgressSchema = zod_1.z.object({
    courseOutlineId: zod_1.z.string().min(1, 'Course outline ID is required'),
    watchTime: zod_1.z.number().min(0, 'Watch time must be positive').optional(),
    isCompleted: zod_1.z.boolean().optional(),
});
exports.getLessonProgressSchema = zod_1.z.object({
    stageId: zod_1.z.string().optional(),
    courseOutlineId: zod_1.z.string().optional(),
    userId: zod_1.z.string().optional(),
});
exports.bulkUpdateLessonProgressSchema = zod_1.z.object({
    lessons: zod_1.z.array(zod_1.z.object({
        courseOutlineId: zod_1.z.string().min(1, 'Course outline ID is required'),
        watchTime: zod_1.z.number().min(0, 'Watch time must be positive').optional(),
        isCompleted: zod_1.z.boolean().optional(),
    })).min(1, 'At least one lesson is required'),
});
//# sourceMappingURL=lesson.schema.js.map