import { Router } from 'express';
import {
  getL<PERSON>onProgress,
  updateLessonProgress,
  bulkUpdateLessonProgress,
  getAdminLessonProgress,
} from '../controllers/lesson.controller';
import { authenticateUser, authenticateAdmin } from '../middleware/auth.middleware';

const router = Router();

/**
 * @swagger
 * /lessons/progress/{stageId}:
 *   get:
 *     summary: Get lesson progress for a stage
 *     description: Retrieve all lessons in a stage with user's progress information
 *     tags: [Lessons]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: stageId
 *         required: true
 *         schema:
 *           type: string
 *         description: Stage ID to get lessons for
 *     responses:
 *       200:
 *         description: Lesson progress retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/StageProgress'
 *       404:
 *         description: Stage not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/progress/:stageId', authenticateUser, getLessonProgress);

/**
 * @swagger
 * /lessons/progress:
 *   post:
 *     summary: Update lesson progress
 *     description: Update progress for a single lesson (watch time, completion status)
 *     tags: [Lessons]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               courseOutlineId:
 *                 type: string
 *                 description: Course outline (lesson) ID
 *               watchTime:
 *                 type: integer
 *                 description: Seconds watched (optional)
 *                 example: 120
 *               isCompleted:
 *                 type: boolean
 *                 description: Whether lesson is completed (optional)
 *             required:
 *               - courseOutlineId
 *     responses:
 *       200:
 *         description: Lesson progress updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         lessonProgress:
 *                           type: object
 *                           description: Updated lesson progress
 *                         stageProgress:
 *                           type: object
 *                           description: Overall stage progress summary
 *       404:
 *         description: Course outline not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/progress', authenticateUser, updateLessonProgress);

/**
 * @swagger
 * /lessons/progress/bulk:
 *   post:
 *     summary: Bulk update lesson progress
 *     description: Update progress for multiple lessons at once
 *     tags: [Lessons]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lessons:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     courseOutlineId:
 *                       type: string
 *                       description: Course outline (lesson) ID
 *                     watchTime:
 *                       type: integer
 *                       description: Seconds watched (optional)
 *                     isCompleted:
 *                       type: boolean
 *                       description: Whether lesson is completed (optional)
 *                   required:
 *                     - courseOutlineId
 *                 description: Array of lesson updates
 *             required:
 *               - lessons
 *             example:
 *               lessons:
 *                 - courseOutlineId: "course_outline_1_id"
 *                   watchTime: 120
 *                   isCompleted: true
 *                 - courseOutlineId: "course_outline_2_id"
 *                   watchTime: 90
 *                   isCompleted: false
 *     responses:
 *       200:
 *         description: Lesson progress updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         updatedLessons:
 *                           type: integer
 *                           description: Number of lessons updated
 *                         lessons:
 *                           type: array
 *                           description: Updated lesson progress records
 *       400:
 *         description: Validation error or invalid course outline IDs
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/progress/bulk', authenticateUser, bulkUpdateLessonProgress);

/**
 * @swagger
 * /lessons/admin/progress:
 *   get:
 *     summary: Get lesson progress for admin view
 *     description: Retrieve lesson progress data for admin dashboard with filtering options
 *     tags: [Lessons]
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: Filter by specific user ID (optional)
 *       - in: query
 *         name: stageId
 *         schema:
 *           type: string
 *         description: Filter by specific stage ID (optional)
 *     responses:
 *       200:
 *         description: Admin lesson progress retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         progressByUser:
 *                           type: object
 *                           description: Progress data grouped by user and stage
 *                         totalRecords:
 *                           type: integer
 *                           description: Total number of progress records
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/admin/progress', authenticateAdmin, getAdminLessonProgress);

export default router;
