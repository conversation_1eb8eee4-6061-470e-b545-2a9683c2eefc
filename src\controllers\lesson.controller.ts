import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError } from '../utils/response.utils';
import { updateLessonProgressSchema, getLessonProgressSchema, bulkUpdateLessonProgressSchema } from '../schemas/lesson.schema';
import { updateUserStreak } from './streak.controller';

/**
 * Get user's lesson progress for a stage
 */
export const getLessonProgress = async (req: Request, res: Response) => {
  try {
    const { stageId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Get stage with course outlines
    const stage = await prisma.stage.findUnique({
      where: { id: stageId },
      include: {
        courseOutlines: {
          include: {
            lessonProgress: {
              where: { userId },
            },
          },
          orderBy: { order: 'asc' },
        },
      },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Format response with progress information
    const lessonsWithProgress = stage.courseOutlines.map(outline => {
      const progress = outline.lessonProgress[0] || null;

      return {
        id: outline.id,
        title: outline.title,
        description: outline.description,
        videoUrl: outline.videoUrl,
        videoFile: outline.videoFile,
        duration: outline.duration,
        order: outline.order,
        progress: progress ? {
          isCompleted: progress.isCompleted,
          watchTime: progress.watchTime,
          completedAt: progress.completedAt,
          progressPercentage: outline.duration
            ? Math.min(100, Math.round((progress.watchTime / outline.duration) * 100))
            : 0,
        } : {
          isCompleted: false,
          watchTime: 0,
          completedAt: null,
          progressPercentage: 0,
        },
      };
    });

    // Calculate overall stage progress
    const totalLessons = lessonsWithProgress.length;
    const completedLessons = lessonsWithProgress.filter(lesson => lesson.progress.isCompleted).length;
    const stageProgressPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;

    const response = {
      stage: {
        id: stage.id,
        title: stage.title,
        description: stage.description,
        order: stage.order,
      },
      lessons: lessonsWithProgress,
      summary: {
        totalLessons,
        completedLessons,
        progressPercentage: stageProgressPercentage,
      },
    };

    return sendSuccess(res, 'Lesson progress retrieved successfully', response);
  } catch (error) {
    console.error('Get lesson progress error:', error);
    return sendError(res, 'Failed to retrieve lesson progress');
  }
};

/**
 * Update lesson progress
 */
export const updateLessonProgress = async (req: Request, res: Response) => {
  try {
    const validatedData = updateLessonProgressSchema.parse(req.body);
    const { courseOutlineId, watchTime, isCompleted } = validatedData;
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Verify course outline exists
    const courseOutline = await prisma.courseOutline.findUnique({
      where: { id: courseOutlineId },
      include: {
        stage: true,
      },
    });

    if (!courseOutline) {
      return sendError(res, 'Course outline not found', 404);
    }

    // Get or create lesson progress
    let lessonProgress = await prisma.lessonProgress.findUnique({
      where: {
        userId_courseOutlineId: {
          userId,
          courseOutlineId,
        },
      },
    });

    const updateData: any = {};

    if (watchTime !== undefined) {
      updateData.watchTime = watchTime;
      updateData.totalDuration = courseOutline.duration;
    }

    if (isCompleted !== undefined) {
      updateData.isCompleted = isCompleted;
      if (isCompleted) {
        updateData.completedAt = new Date();
      }
    }

    if (lessonProgress) {
      // Update existing progress
      lessonProgress = await prisma.lessonProgress.update({
        where: {
          userId_courseOutlineId: {
            userId,
            courseOutlineId,
          },
        },
        data: updateData,
      });
    } else {
      // Create new progress
      lessonProgress = await prisma.lessonProgress.create({
        data: {
          userId,
          courseOutlineId,
          ...updateData,
        },
      });
    }

    // If lesson was completed, update user's streak
    if (isCompleted) {
      await updateUserStreak(userId);
    }

    // Check if all lessons in the stage are completed
    const stageProgress = await checkStageCompletion(userId, courseOutline.stageId);

    return sendSuccess(res, 'Lesson progress updated successfully', {
      lessonProgress,
      stageProgress,
    });
  } catch (error) {
    console.error('Update lesson progress error:', error);
    return sendError(res, 'Failed to update lesson progress');
  }
};

/**
 * Bulk update lesson progress
 */
export const bulkUpdateLessonProgress = async (req: Request, res: Response) => {
  try {
    const validatedData = bulkUpdateLessonProgressSchema.parse(req.body);
    const { lessons } = validatedData;
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    const updatedLessons = [];
    let hasCompletedLesson = false;

    // Process each lesson update
    for (const lessonUpdate of lessons) {
      const { courseOutlineId, watchTime, isCompleted } = lessonUpdate;

      // Verify course outline exists
      const courseOutline = await prisma.courseOutline.findUnique({
        where: { id: courseOutlineId },
      });

      if (!courseOutline) {
        continue; // Skip invalid course outlines
      }

      const updateData: any = {};

      if (watchTime !== undefined) {
        updateData.watchTime = watchTime;
        updateData.totalDuration = courseOutline.duration;
      }

      if (isCompleted !== undefined) {
        updateData.isCompleted = isCompleted;
        if (isCompleted) {
          updateData.completedAt = new Date();
          hasCompletedLesson = true;
        }
      }

      // Upsert lesson progress
      const lessonProgress = await prisma.lessonProgress.upsert({
        where: {
          userId_courseOutlineId: {
            userId,
            courseOutlineId,
          },
        },
        update: updateData,
        create: {
          userId,
          courseOutlineId,
          ...updateData,
        },
      });

      updatedLessons.push(lessonProgress);
    }

    // If any lesson was completed, update user's streak
    if (hasCompletedLesson) {
      await updateUserStreak(userId);
    }

    return sendSuccess(res, 'Lesson progress updated successfully', {
      updatedLessons: updatedLessons.length,
      lessons: updatedLessons,
    });
  } catch (error) {
    console.error('Bulk update lesson progress error:', error);
    return sendError(res, 'Failed to update lesson progress');
  }
};

/**
 * Check if all lessons in a stage are completed
 */
async function checkStageCompletion(userId: string, stageId: string) {
  try {
    // Get all course outlines for the stage
    const courseOutlines = await prisma.courseOutline.findMany({
      where: { stageId },
      include: {
        lessonProgress: {
          where: { userId },
        },
      },
    });

    const totalLessons = courseOutlines.length;
    const completedLessons = courseOutlines.filter(
      outline => outline.lessonProgress[0]?.isCompleted
    ).length;

    const isStageCompleted = totalLessons > 0 && completedLessons === totalLessons;

    return {
      totalLessons,
      completedLessons,
      isCompleted: isStageCompleted,
      progressPercentage: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0,
    };
  } catch (error) {
    console.error('Check stage completion error:', error);
    return {
      totalLessons: 0,
      completedLessons: 0,
      isCompleted: false,
      progressPercentage: 0,
    };
  }
}

/**
 * Get lesson progress for admin view
 */
export const getAdminLessonProgress = async (req: Request, res: Response) => {
  try {
    const { userId, stageId } = req.query;

    const whereClause: any = {};

    if (userId) {
      whereClause.userId = userId as string;
    }

    if (stageId) {
      whereClause.courseOutline = {
        stageId: stageId as string,
      };
    }

    const lessonProgress = await prisma.lessonProgress.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            phone: true,
          },
        },
        courseOutline: {
          include: {
            stage: {
              select: {
                id: true,
                title: true,
                order: true,
              },
            },
          },
        },
      },
      orderBy: [
        { courseOutline: { stage: { order: 'asc' } } },
        { courseOutline: { order: 'asc' } },
        { updatedAt: 'desc' },
      ],
    });

    // Group by user and stage for better overview
    const progressByUser = lessonProgress.reduce((acc, progress) => {
      const userId = progress.user.id;
      const stageId = progress.courseOutline.stage.id;

      if (!acc[userId]) {
        acc[userId] = {
          user: progress.user,
          stages: {},
        };
      }

      if (!acc[userId].stages[stageId]) {
        acc[userId].stages[stageId] = {
          stage: progress.courseOutline.stage,
          lessons: [],
        };
      }

      acc[userId].stages[stageId].lessons.push({
        id: progress.id,
        courseOutline: {
          id: progress.courseOutline.id,
          title: progress.courseOutline.title,
          order: progress.courseOutline.order,
          duration: progress.courseOutline.duration,
        },
        isCompleted: progress.isCompleted,
        watchTime: progress.watchTime,
        progressPercentage: progress.totalDuration
          ? Math.min(100, Math.round((progress.watchTime / progress.totalDuration) * 100))
          : 0,
        completedAt: progress.completedAt,
      });

      return acc;
    }, {} as any);

    return sendSuccess(res, 'Admin lesson progress retrieved successfully', {
      progressByUser,
      totalRecords: lessonProgress.length,
    });
  } catch (error) {
    console.error('Get admin lesson progress error:', error);
    return sendError(res, 'Failed to retrieve lesson progress');
  }
};
