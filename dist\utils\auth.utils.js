"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateQuizScore = exports.normalizePhoneNumber = exports.generateUserCode = exports.generateOTP = exports.comparePassword = exports.hashPassword = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
const hashPassword = async (password) => {
    const saltRounds = 12;
    return bcryptjs_1.default.hash(password, saltRounds);
};
exports.hashPassword = hashPassword;
const comparePassword = async (password, hash) => {
    return bcryptjs_1.default.compare(password, hash);
};
exports.comparePassword = comparePassword;
const generateOTP = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
};
exports.generateOTP = generateOTP;
const generateUserCode = () => {
    return crypto_1.default.randomBytes(4).toString('hex').toUpperCase();
};
exports.generateUserCode = generateUserCode;
const normalizePhoneNumber = (phone) => {
    const digits = phone.replace(/\D/g, '');
    if (digits.startsWith('234')) {
        return `+${digits}`;
    }
    else if (digits.startsWith('0')) {
        return `+234${digits.substring(1)}`;
    }
    else if (digits.length === 10) {
        return `+234${digits}`;
    }
    return `+${digits}`;
};
exports.normalizePhoneNumber = normalizePhoneNumber;
const calculateQuizScore = (userAnswers, correctAnswers) => {
    if (userAnswers.length === 0 || correctAnswers.length === 0) {
        return 0;
    }
    let correctCount = 0;
    userAnswers.forEach(userAnswer => {
        const correctAnswer = correctAnswers.find(ca => ca.questionId === userAnswer.questionId);
        if (correctAnswer) {
            const userSet = new Set(userAnswer.selectedAnswers.sort());
            const correctSet = new Set(correctAnswer.correctAnswers.sort());
            if (userSet.size === correctSet.size &&
                [...userSet].every(answer => correctSet.has(answer))) {
                correctCount++;
            }
        }
    });
    return Math.round((correctCount / userAnswers.length) * 100);
};
exports.calculateQuizScore = calculateQuizScore;
//# sourceMappingURL=auth.utils.js.map