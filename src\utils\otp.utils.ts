import { generateOTP } from './auth.utils';

// Twilio client - will be initialized when needed
let twilioClient: any = null;

/**
 * Initialize Twilio client
 */
function getTwilioClient() {
  if (!twilioClient) {
    try {
      const twilio = require('twilio');
      twilioClient = twilio(
        process.env.TWILIO_ACCOUNT_SID,
        process.env.TWILIO_AUTH_TOKEN
      );
    } catch (error) {
      console.error('Failed to initialize Twilio client:', error);
      console.log('Make sure to install Twilio: npm install twilio');
      return null;
    }
  }
  return twilioClient;
}

/**
 * OTP service with Twilio SMS integration
 */
export class OTPService {
  /**
   * Send OTP via SMS using Twilio
   * @param phone - Phone number to send OTP to
   * @param otp - OTP code to send
   */
  static async sendOTP(phone: string, otp: string): Promise<boolean> {
    try {
      const client = getTwilioClient();

      if (!client) {
        // Fallback to console logging if Twi<PERSON> is not available
        console.log(`📱 [FALLBACK] Sending OTP ${otp} to ${phone}`);
        console.log('⚠️  Twilio not configured. Install with: npm install twilio');
        return true; // Return true for development
      }

      // Check if Twilio credentials are configured
      if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN || !process.env.TWILIO_PHONE_NUMBER) {
        console.log(`📱 [FALLBACK] Sending OTP ${otp} to ${phone}`);
        console.log('⚠️  Twilio credentials not configured in .env file');
        return true; // Return true for development
      }

      console.log(`📱 Sending OTP ${otp} to ${phone} via Twilio...`);

      const message = await client.messages.create({
        body: `Your CozaConnect verification code is: ${otp}. Valid for 10 minutes. Do not share this code with anyone.`,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phone
      });

      console.log(`✅ SMS sent successfully. Message SID: ${message.sid}`);
      return true;
    } catch (error: any) {
      console.error('Failed to send OTP via Twilio:', error);

      // Log specific Twilio errors
      if (error.code) {
        console.error(`Twilio Error Code: ${error.code}`);
        console.error(`Twilio Error Message: ${error.message}`);
      }

      // Fallback to console logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`📱 [FALLBACK] Would send OTP ${otp} to ${phone}`);
        return true;
      }

      return false;
    }
  }

  /**
   * Generate and send OTP
   * @param phone - Phone number to send OTP to
   */
  static async generateAndSendOTP(phone: string): Promise<{ otp: string; success: boolean }> {
    const otp = generateOTP();
    const success = await this.sendOTP(phone, otp);

    return { otp, success };
  }
}
