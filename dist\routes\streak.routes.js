"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const streak_controller_1 = require("../controllers/streak.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.get('/my-streak', auth_middleware_1.authenticateUser, streak_controller_1.getUserStreak);
router.post('/update', auth_middleware_1.authenticateUser, streak_controller_1.manualUpdateStreak);
router.get('/leaderboard', auth_middleware_1.authenticateAdmin, streak_controller_1.getStreakLeaderboard);
exports.default = router;
//# sourceMappingURL=streak.routes.js.map