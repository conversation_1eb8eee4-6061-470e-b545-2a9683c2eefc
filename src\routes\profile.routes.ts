import { Router } from 'express';
import {
  getProfile,
  updateProfile,
  changePin,
  uploadProfileImage,
  deleteProfileImage,
} from '../controllers/profile.controller';
import { authenticateUser } from '../middleware/auth.middleware';
import { validateBody, validateQuery } from '../middleware/validation.middleware';
import { updateProfileSchema, changePinSchema, getProfileSchema } from '../schemas/profile.schema';
import { upload } from '../config/cloudinary.config';

const router = Router();

// All profile routes require authentication
router.use(authenticateUser);

// Profile management
router.get('/', validateQuery(getProfileSchema), getProfile);
router.put('/', validateBody(updateProfileSchema), updateProfile);

// PIN management
router.post('/change-pin', validateBody(changePinSchema), changePin);

// Profile image management
router.post('/upload-image', upload.single('profileImage'), uploadProfileImage);
router.delete('/image', deleteProfileImage);

export default router;
