import { Router } from 'express';
import {
  getProfile,
  updateProfile,
  changePin,
  uploadProfileImage,
  deleteProfileImage,
} from '../controllers/profile.controller';
import { authenticateUser } from '../middleware/auth.middleware';
import { validateBody, validateQuery } from '../middleware/validation.middleware';
import { updateProfileSchema, changePinSchema, getProfileSchema } from '../schemas/profile.schema';
import { upload } from '../config/cloudinary.config';

const router = Router();

// All profile routes require authentication
router.use(authenticateUser);

/**
 * @swagger
 * /profile:
 *   get:
 *     summary: Get user profile
 *     description: Retrieve user profile information with optional additional data (progress, badges, streak)
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: includeProgress
 *         schema:
 *           type: boolean
 *         description: Include user's stage progress
 *       - in: query
 *         name: includeBadges
 *         schema:
 *           type: boolean
 *         description: Include user's earned badges
 *       - in: query
 *         name: includeStreak
 *         schema:
 *           type: boolean
 *         description: Include user's streak information
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/ProfileWithStats'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update user profile
 *     description: Update user profile information
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ProfileUpdate'
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/', validateQuery(getProfileSchema), getProfile);
router.put('/', validateBody(updateProfileSchema), updateProfile);

/**
 * @swagger
 * /profile/change-pin:
 *   post:
 *     summary: Change user PIN
 *     description: Change user's 4-digit PIN for authentication
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PinChange'
 *     responses:
 *       200:
 *         description: PIN changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: Invalid current PIN or validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/change-pin', validateBody(changePinSchema), changePin);

/**
 * @swagger
 * /profile/upload-image:
 *   post:
 *     summary: Upload profile image
 *     description: Upload a new profile image to Cloudinary
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               profileImage:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Profile image uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         profileImage:
 *                           type: string
 *                           description: Cloudinary image URL
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *       400:
 *         description: No image file uploaded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/upload-image', upload.single('profileImage'), uploadProfileImage);

/**
 * @swagger
 * /profile/image:
 *   delete:
 *     summary: Delete profile image
 *     description: Remove the user's profile image
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile image deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.delete('/image', deleteProfileImage);

export default router;
