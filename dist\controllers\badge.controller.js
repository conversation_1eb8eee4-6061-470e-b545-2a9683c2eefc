"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteBadge = exports.updateBadge = exports.shareBadge = exports.awardBadge = exports.getUserBadges = exports.getAllBadges = exports.createBadge = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const badge_schema_1 = require("../schemas/badge.schema");
const createBadge = async (req, res) => {
    try {
        const validatedData = badge_schema_1.createBadgeSchema.parse(req.body);
        const { name, description, icon, stageId } = validatedData;
        const existingBadge = await db_1.default.badge.findUnique({
            where: { name },
        });
        if (existingBadge) {
            return (0, response_utils_1.sendError)(res, 'Badge with this name already exists', 400);
        }
        if (stageId) {
            const stage = await db_1.default.stage.findUnique({
                where: { id: stageId },
            });
            if (!stage) {
                return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
            }
        }
        const badge = await db_1.default.badge.create({
            data: {
                name,
                description,
                icon,
                stageId,
            },
            include: {
                stage: {
                    select: {
                        id: true,
                        title: true,
                        order: true,
                    },
                },
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Badge created successfully', badge, 201);
    }
    catch (error) {
        console.error('Create badge error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to create badge');
    }
};
exports.createBadge = createBadge;
const getAllBadges = async (req, res) => {
    try {
        const badges = await db_1.default.badge.findMany({
            include: {
                stage: {
                    select: {
                        id: true,
                        title: true,
                        order: true,
                    },
                },
                _count: {
                    select: {
                        userBadges: true,
                    },
                },
            },
            orderBy: [
                { stage: { order: 'asc' } },
                { createdAt: 'asc' },
            ],
        });
        return (0, response_utils_1.sendSuccess)(res, 'Badges retrieved successfully', badges);
    }
    catch (error) {
        console.error('Get badges error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve badges');
    }
};
exports.getAllBadges = getAllBadges;
const getUserBadges = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const userBadges = await db_1.default.userBadge.findMany({
            where: { userId },
            include: {
                badge: {
                    include: {
                        stage: {
                            select: {
                                id: true,
                                title: true,
                                order: true,
                            },
                        },
                    },
                },
            },
            orderBy: { earnedAt: 'desc' },
        });
        const badges = userBadges.map(userBadge => ({
            id: userBadge.badge.id,
            name: userBadge.badge.name,
            description: userBadge.badge.description,
            icon: userBadge.badge.icon,
            stage: userBadge.badge.stage,
            earnedAt: userBadge.earnedAt,
            isShared: userBadge.isShared,
        }));
        return (0, response_utils_1.sendSuccess)(res, 'User badges retrieved successfully', badges);
    }
    catch (error) {
        console.error('Get user badges error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve user badges');
    }
};
exports.getUserBadges = getUserBadges;
const awardBadge = async (userId, stageId) => {
    try {
        const badge = await db_1.default.badge.findFirst({
            where: { stageId, isActive: true },
        });
        if (!badge) {
            console.log(`No badge found for stage ${stageId}`);
            return null;
        }
        const existingUserBadge = await db_1.default.userBadge.findUnique({
            where: {
                userId_badgeId: {
                    userId,
                    badgeId: badge.id,
                },
            },
            include: {
                badge: true,
            },
        });
        if (existingUserBadge) {
            console.log(`User ${userId} already has badge ${badge.id}`);
            return existingUserBadge;
        }
        const userBadge = await db_1.default.userBadge.create({
            data: {
                userId,
                badgeId: badge.id,
            },
            include: {
                badge: true,
            },
        });
        console.log(`Badge ${badge.name} awarded to user ${userId}`);
        return userBadge;
    }
    catch (error) {
        console.error('Award badge error:', error);
        return null;
    }
};
exports.awardBadge = awardBadge;
const shareBadge = async (req, res) => {
    try {
        const validatedData = badge_schema_1.shareBadgeSchema.parse(req.body);
        const { badgeId, platform } = validatedData;
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const userBadge = await db_1.default.userBadge.findUnique({
            where: {
                userId_badgeId: {
                    userId,
                    badgeId,
                },
            },
            include: {
                badge: {
                    include: {
                        stage: true,
                    },
                },
            },
        });
        if (!userBadge) {
            return (0, response_utils_1.sendError)(res, 'Badge not found or not earned by user', 404);
        }
        await db_1.default.userBadge.update({
            where: {
                userId_badgeId: {
                    userId,
                    badgeId,
                },
            },
            data: { isShared: true },
        });
        const shareContent = {
            title: `I earned the "${userBadge.badge.name}" badge!`,
            description: userBadge.badge.description,
            stage: userBadge.badge.stage?.title,
            url: `${process.env.FRONTEND_URL}/badges/${badgeId}`,
            image: userBadge.badge.icon,
        };
        return (0, response_utils_1.sendSuccess)(res, 'Badge shared successfully', shareContent);
    }
    catch (error) {
        console.error('Share badge error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to share badge');
    }
};
exports.shareBadge = shareBadge;
const updateBadge = async (req, res) => {
    try {
        const { id } = req.params;
        const validatedData = badge_schema_1.updateBadgeSchema.parse(req.body);
        const badge = await db_1.default.badge.findUnique({
            where: { id },
        });
        if (!badge) {
            return (0, response_utils_1.sendError)(res, 'Badge not found', 404);
        }
        const updatedBadge = await db_1.default.badge.update({
            where: { id },
            data: validatedData,
            include: {
                stage: {
                    select: {
                        id: true,
                        title: true,
                        order: true,
                    },
                },
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Badge updated successfully', updatedBadge);
    }
    catch (error) {
        console.error('Update badge error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to update badge');
    }
};
exports.updateBadge = updateBadge;
const deleteBadge = async (req, res) => {
    try {
        const { id } = req.params;
        const badge = await db_1.default.badge.findUnique({
            where: { id },
        });
        if (!badge) {
            return (0, response_utils_1.sendError)(res, 'Badge not found', 404);
        }
        await db_1.default.badge.delete({
            where: { id },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Badge deleted successfully');
    }
    catch (error) {
        console.error('Delete badge error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to delete badge');
    }
};
exports.deleteBadge = deleteBadge;
//# sourceMappingURL=badge.controller.js.map