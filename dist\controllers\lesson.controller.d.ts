import { Request, Response } from 'express';
export declare const getLessonProgress: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateLessonProgress: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const bulkUpdateLessonProgress: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getAdminLessonProgress: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=lesson.controller.d.ts.map