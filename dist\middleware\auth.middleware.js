"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAdminRole = exports.authenticateAdmin = exports.authenticateUser = exports.generateAdminToken = exports.generateUserToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const response_utils_1 = require("../utils/response.utils");
const db_1 = __importDefault(require("../db/db"));
const generateUserToken = (user) => {
    const payload = {
        id: user.id,
        phone: user.phone,
        fullName: user.fullName,
        type: 'user',
    };
    return jsonwebtoken_1.default.sign(payload, process.env.JWT_SECRET || 'your-secret-key', {
        expiresIn: '30d',
    });
};
exports.generateUserToken = generateUserToken;
const generateAdminToken = (admin) => {
    const payload = {
        id: admin.id,
        username: admin.username,
        fullName: admin.fullName,
        role: admin.role,
        type: 'admin',
    };
    return jsonwebtoken_1.default.sign(payload, process.env.JWT_SECRET || 'your-secret-key', {
        expiresIn: '24h',
    });
};
exports.generateAdminToken = generateAdminToken;
const authenticateUser = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return (0, response_utils_1.sendError)(res, 'Access token required', 401);
        }
        const token = authHeader.substring(7);
        try {
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'your-secret-key');
            if (decoded.type !== 'user') {
                return (0, response_utils_1.sendError)(res, 'Invalid token type', 401);
            }
            const user = await db_1.default.user.findFirst({
                where: {
                    id: decoded.id,
                    isActive: true,
                },
                select: {
                    id: true,
                    phone: true,
                    fullName: true,
                },
            });
            if (!user) {
                return (0, response_utils_1.sendError)(res, 'User not found or inactive', 401);
            }
            req.user = {
                ...user,
                type: 'user',
            };
            next();
        }
        catch (jwtError) {
            return (0, response_utils_1.sendError)(res, 'Invalid or expired token', 401);
        }
    }
    catch (error) {
        console.error('Authentication error:', error);
        return (0, response_utils_1.sendError)(res, 'Authentication failed', 500);
    }
};
exports.authenticateUser = authenticateUser;
const authenticateAdmin = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return (0, response_utils_1.sendError)(res, 'Access token required', 401);
        }
        const token = authHeader.substring(7);
        try {
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'your-secret-key');
            if (decoded.type !== 'admin') {
                return (0, response_utils_1.sendError)(res, 'Invalid token type', 401);
            }
            const admin = await db_1.default.admin.findFirst({
                where: {
                    id: decoded.id,
                    isActive: true,
                },
                select: {
                    id: true,
                    username: true,
                    fullName: true,
                    role: true,
                },
            });
            if (!admin) {
                return (0, response_utils_1.sendError)(res, 'Admin not found or inactive', 401);
            }
            req.admin = {
                ...admin,
                type: 'admin',
            };
            next();
        }
        catch (jwtError) {
            return (0, response_utils_1.sendError)(res, 'Invalid or expired token', 401);
        }
    }
    catch (error) {
        console.error('Admin authentication error:', error);
        return (0, response_utils_1.sendError)(res, 'Authentication failed', 500);
    }
};
exports.authenticateAdmin = authenticateAdmin;
const requireAdminRole = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.admin) {
            return (0, response_utils_1.sendError)(res, 'Admin authentication required', 401);
        }
        if (!allowedRoles.includes(req.admin.role)) {
            return (0, response_utils_1.sendError)(res, 'Insufficient permissions', 403);
        }
        next();
    };
};
exports.requireAdminRole = requireAdminRole;
//# sourceMappingURL=auth.middleware.js.map