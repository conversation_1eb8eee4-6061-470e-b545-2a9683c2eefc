import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError, sendServerError } from '../utils/response.utils';
import { hashPassword, comparePassword, normalizePhoneNumber } from '../utils/auth.utils';
import { OTPService } from '../utils/otp.utils';
import { generateUserToken } from '../middleware/auth.middleware';
import {
  SendOtpRequest,
  VerifyOtpRequest,
  RegisterRequest,
  LoginRequest,
  ForgotPinRequest,
} from '../schemas/auth.schema';

/**
 * Send OTP to user's phone number
 */
export const sendOtp = async (req: Request, res: Response) => {
  try {
    const { phone }: SendOtpRequest = req.body;
    const normalizedPhone = normalizePhoneNumber(phone);

    // Generate and send OTP
    const { otp, success } = await OTPService.generateAndSendOTP(normalizedPhone);

    if (!success) {
      return sendError(res, 'Failed to send OTP. Please try again.', 500);
    }

    // Store OTP in database with expiration (10 minutes)
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    await prisma.oTPVerification.create({
      data: {
        phone: normalizedPhone,
        otp,
        expiresAt,
      },
    });

    return sendSuccess(res, 'OTP sent successfully', {
      phone: normalizedPhone,
      expiresIn: 600, // 10 minutes in seconds
    });
  } catch (error) {
    console.error('Send OTP error:', error);
    return sendServerError(res, 'Failed to send OTP');
  }
};

/**
 * Verify OTP
 */
export const verifyOtp = async (req: Request, res: Response) => {
  try {
    const { phone, otp }: VerifyOtpRequest = req.body;
    const normalizedPhone = normalizePhoneNumber(phone);

    // Find valid OTP
    const otpRecord = await prisma.oTPVerification.findFirst({
      where: {
        phone: normalizedPhone,
        otp,
        isUsed: false,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!otpRecord) {
      return sendError(res, 'Invalid or expired OTP', 400);
    }

    // Mark OTP as used
    await prisma.oTPVerification.update({
      where: { id: otpRecord.id },
      data: { isUsed: true },
    });

    return sendSuccess(res, 'OTP verified successfully', {
      phone: normalizedPhone,
      verified: true,
    });
  } catch (error) {
    console.error('Verify OTP error:', error);
    return sendServerError(res, 'Failed to verify OTP');
  }
};

/**
 * Register new user
 * Requires phone number to be verified first via /verify-otp
 */
export const register = async (req: Request, res: Response) => {
  try {
    const { phone, fullName, pin }: Omit<RegisterRequest, 'otp'> = req.body;
    const normalizedPhone = normalizePhoneNumber(phone);

    // Check if phone number was recently verified (within last 30 minutes)
    const recentVerification = await prisma.oTPVerification.findFirst({
      where: {
        phone: normalizedPhone,
        isUsed: true, // Must be used (verified)
        expiresAt: {
          gt: new Date(Date.now() - 30 * 60 * 1000), // Within last 30 minutes
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (!recentVerification) {
      return sendError(res, 'Phone number not verified. Please verify your phone number first.', 400);
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { phone: normalizedPhone },
    });

    if (existingUser) {
      return sendError(res, 'User with this phone number already exists', 409);
    }

    // Hash PIN
    const hashedPin = await hashPassword(pin);

    // Create user
    const user = await prisma.user.create({
      data: {
        phone: normalizedPhone,
        fullName: fullName.trim(),
        pin: hashedPin,
      },
      select: {
        id: true,
        phone: true,
        fullName: true,
        points: true,
        createdAt: true,
      },
    });

    // Generate token
    const token = generateUserToken(user);

    return sendSuccess(res, 'Registration successful', {
      user,
      token,
    }, 201);
  } catch (error) {
    console.error('Registration error:', error);
    return sendServerError(res, 'Registration failed');
  }
};

/**
 * User login
 */
export const login = async (req: Request, res: Response) => {
  try {
    const { phone, pin }: LoginRequest = req.body;
    const normalizedPhone = normalizePhoneNumber(phone);

    // Find user
    const user = await prisma.user.findUnique({
      where: { phone: normalizedPhone },
      select: {
        id: true,
        phone: true,
        fullName: true,
        pin: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      return sendError(res, 'Invalid phone number or PIN', 401);
    }

    // Verify PIN
    const isPinValid = await comparePassword(pin, user.pin);
    if (!isPinValid) {
      return sendError(res, 'Invalid phone number or PIN', 401);
    }

    // Generate token
    const token = generateUserToken({
      id: user.id,
      phone: user.phone,
      fullName: user.fullName,
    });

    return sendSuccess(res, 'Login successful', {
      user: {
        id: user.id,
        phone: user.phone,
        fullName: user.fullName,
      },
      token,
    });
  } catch (error) {
    console.error('Login error:', error);
    return sendServerError(res, 'Login failed');
  }
};

/**
 * Forgot PIN - Reset PIN with OTP verification
 */
export const forgotPin = async (req: Request, res: Response) => {
  try {
    const { phone, otp, newPin }: ForgotPinRequest = req.body;
    const normalizedPhone = normalizePhoneNumber(phone);

    // Verify OTP
    const otpRecord = await prisma.oTPVerification.findFirst({
      where: {
        phone: normalizedPhone,
        otp,
        isUsed: false,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!otpRecord) {
      return sendError(res, 'Invalid or expired OTP', 400);
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { phone: normalizedPhone },
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Hash new PIN
    const hashedPin = await hashPassword(newPin);

    // Update user's PIN
    await prisma.user.update({
      where: { id: user.id },
      data: { pin: hashedPin },
    });

    // Mark OTP as used
    await prisma.oTPVerification.update({
      where: { id: otpRecord.id },
      data: { isUsed: true },
    });

    return sendSuccess(res, 'PIN reset successful');
  } catch (error) {
    console.error('Forgot PIN error:', error);
    return sendServerError(res, 'Failed to reset PIN');
  }
};

/**
 * Get current user profile
 */
export const getProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        phone: true,
        fullName: true,
        createdAt: true,
        buddy: {
          select: {
            id: true,
            fullName: true,
            phone: true,
          },
        },
        progress: {
          include: {
            stage: {
              select: {
                id: true,
                title: true,
                order: true,
              },
            },
          },
          orderBy: {
            stage: {
              order: 'asc',
            },
          },
        },
      },
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    return sendSuccess(res, 'Profile retrieved successfully', user);
  } catch (error) {
    console.error('Get profile error:', error);
    return sendServerError(res, 'Failed to get profile');
  }
};
