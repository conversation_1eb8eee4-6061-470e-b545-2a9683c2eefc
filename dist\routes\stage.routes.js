"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const stage_controller_1 = require("../controllers/stage.controller");
const validation_middleware_1 = require("../middleware/validation.middleware");
const auth_middleware_1 = require("../middleware/auth.middleware");
const stage_schema_1 = require("../schemas/stage.schema");
const zod_1 = require("zod");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authenticateUser);
const stageIdSchema = zod_1.z.object({
    id: zod_1.z.string().min(1, 'Stage ID is required'),
});
router.get('/', stage_controller_1.getStages);
router.get('/progress', stage_controller_1.getUserProgress);
router.get('/points', stage_controller_1.getUserPoints);
router.get('/:id', (0, validation_middleware_1.validateParams)(stageIdSchema), stage_controller_1.getStageById);
router.post('/complete', (0, validation_middleware_1.validateBody)(stage_schema_1.completeStageSchema), stage_controller_1.completeStage);
exports.default = router;
//# sourceMappingURL=stage.routes.js.map