import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError } from '../utils/response.utils';
import { createBadgeSchema, updateBadgeSchema, shareBadgeSchema } from '../schemas/badge.schema';

/**
 * Create a new badge (Admin only)
 */
export const createBadge = async (req: Request, res: Response) => {
  try {
    const validatedData = createBadgeSchema.parse(req.body);
    const { name, description, icon, stageId } = validatedData;

    // Check if badge name already exists
    const existingBadge = await prisma.badge.findUnique({
      where: { name },
    });

    if (existingBadge) {
      return sendError(res, 'Badge with this name already exists', 400);
    }

    // If stageId provided, verify stage exists
    if (stageId) {
      const stage = await prisma.stage.findUnique({
        where: { id: stageId },
      });

      if (!stage) {
        return sendError(res, 'Stage not found', 404);
      }
    }

    const badge = await prisma.badge.create({
      data: {
        name,
        description,
        icon,
        stageId,
      },
      include: {
        stage: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
      },
    });

    return sendSuccess(res, 'Badge created successfully', badge, 201);
  } catch (error) {
    console.error('Create badge error:', error);
    return sendError(res, 'Failed to create badge');
  }
};

/**
 * Get all badges (Admin view)
 */
export const getAllBadges = async (req: Request, res: Response) => {
  try {
    const badges = await prisma.badge.findMany({
      include: {
        stage: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
        _count: {
          select: {
            userBadges: true, // Count how many users earned this badge
          },
        },
      },
      orderBy: [
        { stage: { order: 'asc' } },
        { createdAt: 'asc' },
      ],
    });

    return sendSuccess(res, 'Badges retrieved successfully', badges);
  } catch (error) {
    console.error('Get badges error:', error);
    return sendError(res, 'Failed to retrieve badges');
  }
};

/**
 * Get user's badges
 */
export const getUserBadges = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    const userBadges = await prisma.userBadge.findMany({
      where: { userId },
      include: {
        badge: {
          include: {
            stage: {
              select: {
                id: true,
                title: true,
                order: true,
              },
            },
          },
        },
      },
      orderBy: { earnedAt: 'desc' },
    });

    // Format response
    const badges = userBadges.map(userBadge => ({
      id: userBadge.badge.id,
      name: userBadge.badge.name,
      description: userBadge.badge.description,
      icon: userBadge.badge.icon,
      stage: userBadge.badge.stage,
      earnedAt: userBadge.earnedAt,
      isShared: userBadge.isShared,
    }));

    return sendSuccess(res, 'User badges retrieved successfully', badges);
  } catch (error) {
    console.error('Get user badges error:', error);
    return sendError(res, 'Failed to retrieve user badges');
  }
};

/**
 * Award badge to user (automatically called when stage is completed)
 */
export const awardBadge = async (userId: string, stageId: string) => {
  try {
    // Find badge for this stage
    const badge = await prisma.badge.findFirst({
      where: { stageId, isActive: true },
    });

    if (!badge) {
      console.log(`No badge found for stage ${stageId}`);
      return null;
    }

    // Check if user already has this badge
    const existingUserBadge = await prisma.userBadge.findUnique({
      where: {
        userId_badgeId: {
          userId,
          badgeId: badge.id,
        },
      },
    });

    if (existingUserBadge) {
      console.log(`User ${userId} already has badge ${badge.id}`);
      return existingUserBadge;
    }

    // Award badge to user
    const userBadge = await prisma.userBadge.create({
      data: {
        userId,
        badgeId: badge.id,
      },
      include: {
        badge: true,
      },
    });

    console.log(`Badge ${badge.name} awarded to user ${userId}`);
    return userBadge;
  } catch (error) {
    console.error('Award badge error:', error);
    return null;
  }
};

/**
 * Share badge
 */
export const shareBadge = async (req: Request, res: Response) => {
  try {
    const validatedData = shareBadgeSchema.parse(req.body);
    const { badgeId, platform } = validatedData;
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Verify user has this badge
    const userBadge = await prisma.userBadge.findUnique({
      where: {
        userId_badgeId: {
          userId,
          badgeId,
        },
      },
      include: {
        badge: {
          include: {
            stage: true,
          },
        },
      },
    });

    if (!userBadge) {
      return sendError(res, 'Badge not found or not earned by user', 404);
    }

    // Mark badge as shared
    await prisma.userBadge.update({
      where: {
        userId_badgeId: {
          userId,
          badgeId,
        },
      },
      data: { isShared: true },
    });

    // Generate share content
    const shareContent = {
      title: `I earned the "${userBadge.badge.name}" badge!`,
      description: userBadge.badge.description,
      stage: userBadge.badge.stage?.title,
      url: `${process.env.FRONTEND_URL}/badges/${badgeId}`,
      image: userBadge.badge.icon,
    };

    return sendSuccess(res, 'Badge shared successfully', shareContent);
  } catch (error) {
    console.error('Share badge error:', error);
    return sendError(res, 'Failed to share badge');
  }
};

/**
 * Update badge (Admin only)
 */
export const updateBadge = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const validatedData = updateBadgeSchema.parse(req.body);

    const badge = await prisma.badge.findUnique({
      where: { id },
    });

    if (!badge) {
      return sendError(res, 'Badge not found', 404);
    }

    const updatedBadge = await prisma.badge.update({
      where: { id },
      data: validatedData,
      include: {
        stage: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
      },
    });

    return sendSuccess(res, 'Badge updated successfully', updatedBadge);
  } catch (error) {
    console.error('Update badge error:', error);
    return sendError(res, 'Failed to update badge');
  }
};

/**
 * Delete badge (Admin only)
 */
export const deleteBadge = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const badge = await prisma.badge.findUnique({
      where: { id },
    });

    if (!badge) {
      return sendError(res, 'Badge not found', 404);
    }

    await prisma.badge.delete({
      where: { id },
    });

    return sendSuccess(res, 'Badge deleted successfully');
  } catch (error) {
    console.error('Delete badge error:', error);
    return sendError(res, 'Failed to delete badge');
  }
};
