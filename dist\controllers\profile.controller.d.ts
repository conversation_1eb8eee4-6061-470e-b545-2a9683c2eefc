import { Request, Response } from 'express';
export declare const getProfile: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateProfile: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const changePin: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const uploadProfileImage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const deleteProfileImage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=profile.controller.d.ts.map