{"name": "coza-connect", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "tsnd --respawn -r tsconfig-paths/register --pretty --transpile-only ./src/index.ts", "build": "tsc && tsc-alias", "start": "node ./dist", "postinstall": "prisma generate", "db:seed": "tsnd --transpile-only ./prisma/seed.ts", "db:push": "prisma db push", "db:studio": "prisma studio"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.5.1", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4"}, "dependencies": {"@prisma/client": "^5.19.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "prisma": "^5.19.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.1", "zod": "^3.25.67"}}