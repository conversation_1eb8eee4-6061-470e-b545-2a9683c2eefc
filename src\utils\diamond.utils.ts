import prisma from '../db/db';

/**
 * Default diamond system constants
 */
export const DIAMOND_CONFIG = {
  DEFAULT_GIFT_VALUE_PER_DIAMOND: 3000, // Default points per diamond
} as const;

/**
 * Get admin-defined gift value per diamond from system config
 */
export const getGiftValuePerDiamond = async (): Promise<number> => {
  try {
    const config = await prisma.systemConfig.findUnique({
      where: { key: 'GIFT_VALUE_PER_DIAMOND' },
    });

    if (config && config.value) {
      const value = parseInt(config.value);
      return isNaN(value) ? DIAMOND_CONFIG.DEFAULT_GIFT_VALUE_PER_DIAMOND : value;
    }

    return DIAMOND_CONFIG.DEFAULT_GIFT_VALUE_PER_DIAMOND;
  } catch (error) {
    console.error('Error getting gift value per diamond:', error);
    return DIAMOND_CONFIG.DEFAULT_GIFT_VALUE_PER_DIAMOND;
  }
};

/**
 * Set admin-defined gift value per diamond
 */
export const setGiftValuePerDiamond = async (value: number): Promise<void> => {
  await prisma.systemConfig.upsert({
    where: { key: 'GIFT_VALUE_PER_DIAMOND' },
    update: {
      value: value.toString(),
      description: 'Points required per diamond/gift'
    },
    create: {
      key: 'GIFT_VALUE_PER_DIAMOND',
      value: value.toString(),
      description: 'Points required per diamond/gift'
    },
  });
};

/**
 * Calculate diamonds earned from stage points
 */
export const calculateDiamondsFromStagePoints = async (stagePoints: number): Promise<number> => {
  const giftValuePerDiamond = await getGiftValuePerDiamond();

  if (stagePoints < giftValuePerDiamond) {
    return 0;
  }

  // Calculate diamonds: Total Points / Gift Value = Diamonds (rounded down)
  return Math.floor(stagePoints / giftValuePerDiamond);
};

/**
 * Award diamonds for stage completion
 */
export const awardDiamondsForStage = async (
  userId: string,
  stageId: string,
  stageTitle: string,
  pointsEarned: number
): Promise<{ diamonds: number; giftRedemption: any | null }> => {
  const diamonds = await calculateDiamondsFromStagePoints(pointsEarned);

  if (diamonds === 0) {
    return { diamonds: 0, giftRedemption: null };
  }

  // Create gift redemption record for stage-based diamonds
  const giftRedemption = await prisma.giftRedemption.create({
    data: {
      userId,
      adminId: '000000000000000000000000', // Placeholder - will be updated when admin processes
      milestone: `${stageTitle} Completed - ${diamonds} Diamond${diamonds > 1 ? 's' : ''} Earned`,
      stageId,
      pointsEarned,
      diamondsAwarded: diamonds,
    },
  });

  return { diamonds, giftRedemption };
};

/**
 * Get user's total diamonds earned
 */
export const getUserDiamondsSummary = async (userId: string) => {
  // Get all stage-based gift redemptions (diamonds)
  const stageGifts = await prisma.giftRedemption.findMany({
    where: {
      userId,
      stageId: { not: null }, // Stage-based gifts
      diamondsAwarded: { not: null },
    },
    include: {
      user: {
        select: {
          fullName: true,
          phone: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  const totalDiamonds = stageGifts.reduce((sum, gift) => sum + (gift.diamondsAwarded || 0), 0);
  const redeemedDiamonds = stageGifts
    .filter(gift => gift.isRedeemed)
    .reduce((sum, gift) => sum + (gift.diamondsAwarded || 0), 0);
  const availableDiamonds = totalDiamonds - redeemedDiamonds;

  return {
    totalDiamonds,
    redeemedDiamonds,
    availableDiamonds,
    stageGifts: stageGifts.map(gift => ({
      id: gift.id,
      milestone: gift.milestone,
      diamondsAwarded: gift.diamondsAwarded,
      pointsEarned: gift.pointsEarned,
      isRedeemed: gift.isRedeemed,
      redeemedAt: gift.redeemedAt,
      createdAt: gift.createdAt,
    })),
  };
};

/**
 * Get current gift value configuration for admin
 */
export const getGiftValueConfig = async () => {
  const giftValuePerDiamond = await getGiftValuePerDiamond();

  return {
    giftValuePerDiamond,
    description: `Each diamond requires ${giftValuePerDiamond} points`,
  };
};

/**
 * Recalculate and revoke excess diamonds based on new points balance
 * This is called when points are redeemed to ensure diamond consistency
 */
export const recalculateAndRevokeExcessDiamonds = async (
  userId: string,
  newPointsBalance: number,
  adminId: string,
  excludeGiftIds: string[] = []
) => {
  const giftValuePerDiamond = await getGiftValuePerDiamond();

  // Get all unredeemed stage-based gifts (diamonds)
  const unredeemedStageGifts = await prisma.giftRedemption.findMany({
    where: {
      userId,
      stageId: { not: null },
      diamondsAwarded: { not: null },
      isRedeemed: false,
      id: { notIn: excludeGiftIds },
    },
    orderBy: { createdAt: 'desc' }, // Most recent first for revocation
  });

  // Calculate total unredeemed diamonds
  const totalUnredeemedDiamonds = unredeemedStageGifts.reduce(
    (sum, gift) => sum + (gift.diamondsAwarded || 0), 0
  );

  // Calculate maximum diamonds supported by new points balance
  const maxDiamondsForNewBalance = Math.floor(newPointsBalance / giftValuePerDiamond);

  // Calculate diamonds to revoke
  const diamondsToRevoke = Math.max(0, totalUnredeemedDiamonds - maxDiamondsForNewBalance);

  if (diamondsToRevoke === 0) {
    return { diamondsRevoked: 0, revokedGifts: [] };
  }

  // Revoke excess diamonds starting from most recent
  const revokedGifts = [];
  let diamondsRevoked = 0;

  for (const gift of unredeemedStageGifts) {
    if (diamondsRevoked >= diamondsToRevoke) break;

    const diamondsInThisGift = gift.diamondsAwarded || 0;
    if (diamondsRevoked + diamondsInThisGift <= diamondsToRevoke) {
      // Revoke entire gift
      await prisma.giftRedemption.update({
        where: { id: gift.id },
        data: {
          isRedeemed: true,
          redeemedAt: new Date(),
          adminId,
          milestone: `${gift.milestone} - REVOKED due to insufficient points`,
        },
      });

      revokedGifts.push({
        id: gift.id,
        milestone: gift.milestone,
        diamondsRevoked: diamondsInThisGift,
      });

      diamondsRevoked += diamondsInThisGift;
    }
  }

  return {
    diamondsRevoked,
    revokedGifts,
    newMaxDiamonds: maxDiamondsForNewBalance,
    pointsPerDiamond: giftValuePerDiamond,
  };
};
