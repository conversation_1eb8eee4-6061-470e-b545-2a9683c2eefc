import { z } from 'zod';

// Admin login
export const adminLoginSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

// Create admin
export const createAdminSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username too long'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters').max(100, 'Full name too long'),
  role: z.enum(['SUPER_ADMIN', 'ADMIN', 'MODERATOR']).default('ADMIN'),
});

// Assign buddy
export const assignBuddySchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  buddyId: z.string().min(1, 'Buddy ID is required'),
});

// Create report
export const createReportSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  title: z.string().min(1, 'Report title is required').max(200, 'Title too long'),
  content: z.string().min(1, 'Report content is required'),
  status: z.enum(['ACTIVE', 'NEEDS_HELP', 'COMPLETED', 'PAUSED']).default('ACTIVE'),
  tags: z.array(z.string()).default([]),
});

// Update report
export const updateReportSchema = createReportSchema.partial().omit({ userId: true });

// Search user by phone for gift redemption
export const searchUserByPhoneSchema = z.object({
  phone: z.string().regex(/^(\+234|234|0)?[789][01]\d{8}$/, 'Invalid phone number'),
});

// Gift redemption verification (Legacy - keeping for backward compatibility)
export const verifyGiftRedemptionSchema = z.object({
  phone: z.string().regex(/^(\+234|234|0)?[789][01]\d{8}$/, 'Invalid phone number'),
  userCode: z.string().min(1, 'User code is required'),
});

// Initiate gift redemption with PIN verification
export const initiateGiftRedemptionSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  pointsToRedeem: z.number().int().min(1000, 'Minimum 1000 points required for redemption'),
  pin: z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
});


// File upload schema
export const fileUploadSchema = z.object({
  file: z.any(), // Will be handled by multer
  folder: z.string().optional().default('coza-connect'),
});

export type AdminLoginRequest = z.infer<typeof adminLoginSchema>;
export type CreateAdminRequest = z.infer<typeof createAdminSchema>;
export type AssignBuddyRequest = z.infer<typeof assignBuddySchema>;
export type CreateReportRequest = z.infer<typeof createReportSchema>;
export type UpdateReportRequest = z.infer<typeof updateReportSchema>;
export type SearchUserByPhoneRequest = z.infer<typeof searchUserByPhoneSchema>;
export type VerifyGiftRedemptionRequest = z.infer<typeof verifyGiftRedemptionSchema>;
export type InitiateGiftRedemptionRequest = z.infer<typeof initiateGiftRedemptionSchema>;
export type FileUploadRequest = z.infer<typeof fileUploadSchema>;
