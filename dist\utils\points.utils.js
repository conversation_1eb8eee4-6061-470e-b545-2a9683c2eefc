"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserPointsSummary = exports.checkAndCreatePointGifts = exports.deductPoints = exports.awardPoints = exports.calculatePointsForNextGift = exports.calculateEligibleGifts = exports.calculatePointsFromQuiz = exports.POINTS_CONFIG = void 0;
const db_1 = __importDefault(require("../db/db"));
exports.POINTS_CONFIG = {
    POINTS_PER_CORRECT_ANSWER: 1000,
    FIRST_GIFT_THRESHOLD: 5000,
    SUBSEQUENT_GIFT_INTERVAL: 50000,
};
const calculatePointsFromQuiz = (userAnswers, correctAnswers) => {
    if (userAnswers.length === 0 || correctAnswers.length === 0) {
        return 0;
    }
    let correctCount = 0;
    userAnswers.forEach(userAnswer => {
        const correctAnswer = correctAnswers.find(ca => ca.questionId === userAnswer.questionId);
        if (correctAnswer) {
            const userSet = new Set(userAnswer.selectedAnswers.sort());
            const correctSet = new Set(correctAnswer.correctAnswers.sort());
            if (userSet.size === correctSet.size &&
                [...userSet].every(answer => correctSet.has(answer))) {
                correctCount++;
            }
        }
    });
    return correctCount * exports.POINTS_CONFIG.POINTS_PER_CORRECT_ANSWER;
};
exports.calculatePointsFromQuiz = calculatePointsFromQuiz;
const calculateEligibleGifts = (totalPoints) => {
    if (totalPoints < exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD) {
        return 0;
    }
    let gifts = 1;
    let remainingPoints = totalPoints - exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD;
    gifts += Math.floor(remainingPoints / exports.POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL);
    return gifts;
};
exports.calculateEligibleGifts = calculateEligibleGifts;
const calculatePointsForNextGift = (totalPoints) => {
    if (totalPoints < exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD) {
        return exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD - totalPoints;
    }
    const pointsAfterFirstGift = totalPoints - exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD;
    const remainderPoints = pointsAfterFirstGift % exports.POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL;
    return exports.POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL - remainderPoints;
};
exports.calculatePointsForNextGift = calculatePointsForNextGift;
const awardPoints = async (userId, points, description, stageId) => {
    return await db_1.default.$transaction(async (tx) => {
        const updatedUser = await tx.user.update({
            where: { id: userId },
            data: {
                points: {
                    increment: points,
                },
            },
        });
        await tx.pointsTransaction.create({
            data: {
                userId,
                type: 'EARNED',
                points,
                description,
                stageId,
            },
        });
        return updatedUser;
    });
};
exports.awardPoints = awardPoints;
const deductPoints = async (userId, points, description, giftRedemptionId) => {
    return await db_1.default.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
            where: { id: userId },
            select: { points: true },
        });
        if (!user || user.points < points) {
            throw new Error('Insufficient points');
        }
        const updatedUser = await tx.user.update({
            where: { id: userId },
            data: {
                points: {
                    decrement: points,
                },
            },
        });
        await tx.pointsTransaction.create({
            data: {
                userId,
                type: 'DEDUCTED',
                points: -points,
                description,
                giftRedemptionId,
            },
        });
        return updatedUser;
    });
};
exports.deductPoints = deductPoints;
const checkAndCreatePointGifts = async (userId) => {
    const user = await db_1.default.user.findUnique({
        where: { id: userId },
        select: { points: true },
    });
    if (!user) {
        throw new Error('User not found');
    }
    const eligibleGifts = (0, exports.calculateEligibleGifts)(user.points);
    if (eligibleGifts === 0) {
        return [];
    }
    const existingPointGifts = await db_1.default.giftRedemption.count({
        where: {
            userId,
            pointsRequired: {
                not: null,
            },
        },
    });
    const newGiftsNeeded = eligibleGifts - existingPointGifts;
    if (newGiftsNeeded <= 0) {
        return [];
    }
    const newGifts = [];
    for (let i = 0; i < newGiftsNeeded; i++) {
        const giftNumber = existingPointGifts + i + 1;
        const pointsRequired = giftNumber === 1
            ? exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD
            : exports.POINTS_CONFIG.FIRST_GIFT_THRESHOLD + ((giftNumber - 1) * exports.POINTS_CONFIG.SUBSEQUENT_GIFT_INTERVAL);
        const gift = await db_1.default.giftRedemption.create({
            data: {
                userId,
                adminId: '000000000000000000000000',
                milestone: `${pointsRequired} Points Reached`,
                giftType: 'Points Achievement Gift',
                userCode: generateGiftCode(),
                pointsRequired,
                pointsDeducted: pointsRequired,
            },
        });
        newGifts.push(gift);
    }
    return newGifts;
};
exports.checkAndCreatePointGifts = checkAndCreatePointGifts;
const generateGiftCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};
const getUserPointsSummary = async (userId) => {
    const user = await db_1.default.user.findUnique({
        where: { id: userId },
        select: { points: true },
    });
    if (!user) {
        throw new Error('User not found');
    }
    const eligibleGifts = (0, exports.calculateEligibleGifts)(user.points);
    const pointsForNextGift = (0, exports.calculatePointsForNextGift)(user.points);
    const recentTransactions = await db_1.default.pointsTransaction.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
            type: true,
            points: true,
            description: true,
            createdAt: true,
        },
    });
    return {
        totalPoints: user.points,
        eligibleGifts,
        pointsForNextGift,
        recentTransactions,
    };
};
exports.getUserPointsSummary = getUserPointsSummary;
//# sourceMappingURL=points.utils.js.map