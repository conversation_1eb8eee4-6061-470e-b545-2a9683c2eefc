import { z } from 'zod';

// Phone number validation (Nigerian format)
export const phoneSchema = z.string()
  .regex(/^(\+234|234|0)?[789][01]\d{8}$/, 'Invalid Nigerian phone number format');

// PIN validation (4-5 digits)
export const pinSchema = z.string()
  .min(4, 'PIN must be at least 4 digits')
  .max(5, 'PIN must be at most 5 digits')
  .regex(/^\d+$/, 'PIN must contain only digits');

// OTP validation
export const otpSchema = z.string()
  .length(6, 'OTP must be exactly 6 digits')
  .regex(/^\d+$/, 'OTP must contain only digits');

// Send OTP request
export const sendOtpSchema = z.object({
  phone: phoneSchema,
});

// Verify OTP request
export const verifyOtpSchema = z.object({
  phone: phoneSchema,
  otp: otpSchema,
});

// User registration (OTP no longer required - phone must be verified first)
export const registerSchema = z.object({
  phone: phoneSchema,
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be at most 100 characters')
    .trim(),
  pin: pinSchema,
});

// User login
export const loginSchema = z.object({
  phone: phoneSchema,
  pin: pinSchema,
});

// Forgot PIN request
export const forgotPinSchema = z.object({
  phone: phoneSchema,
  otp: otpSchema,
  newPin: pinSchema,
});

export type SendOtpRequest = z.infer<typeof sendOtpSchema>;
export type VerifyOtpRequest = z.infer<typeof verifyOtpSchema>;
export type RegisterRequest = z.infer<typeof registerSchema>;
export type LoginRequest = z.infer<typeof loginSchema>;
export type ForgotPinRequest = z.infer<typeof forgotPinSchema>;
