/**
 * @swagger
 * /admin/login:
 *   post:
 *     tags: [Admin]
 *     summary: Admin login
 *     description: Authenticate admin user with username and password
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 description: Admin username
 *                 example: "admin"
 *               password:
 *                 type: string
 *                 description: Admin password
 *                 example: "admin123"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         admin:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             username:
 *                               type: string
 *                             fullName:
 *                               type: string
 *                             role:
 *                               type: string
 *                               enum: [SUPER_ADMIN, ADMIN, MODERATOR]
 *                         token:
 *                           type: string
 *                           description: JWT authentication token
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /admin/dashboard:
 *   get:
 *     tags: [Admin]
 *     summary: Get dashboard overview
 *     description: Get dashboard statistics including total users, chart data, and notifications
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 30
 *           default: 7
 *         description: Number of days for chart data
 *     responses:
 *       200:
 *         description: Dashboard overview retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Dashboard overview retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalUsers:
 *                       type: integer
 *                       example: 589234
 *                     newUsers:
 *                       type: integer
 *                       example: 1250
 *                     totalStages:
 *                       type: integer
 *                       example: 10
 *                     completionRate:
 *                       type: integer
 *                       example: 85
 *                     chartData:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                             example: "2025-06-20"
 *                           users:
 *                             type: integer
 *                             example: 150
 *                           label:
 *                             type: string
 *                             example: "Jun 20"
 *                     notifications:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                             example: "James Chukwudi"
 *                           action:
 *                             type: string
 *                             example: "just joined COZA"
 *                           time:
 *                             type: string
 *                             format: date-time
 *                           type:
 *                             type: string
 *                             enum: [user_joined, stage_completed]
 *                     period:
 *                       type: string
 *                       example: "7 days"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /admin/users:
 *   get:
 *     tags: [Admin]
 *     summary: Get all users
 *     description: Get paginated list of all users with their progress and points
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of users per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or phone number
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         users:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                               phone:
 *                                 type: string
 *                               fullName:
 *                                 type: string
 *                               points:
 *                                 type: integer
 *                               createdAt:
 *                                 type: string
 *                                 format: date-time
 *                               buddy:
 *                                 type: object
 *                                 nullable: true
 *                               progress:
 *                                 type: array
 *                                 items:
 *                                   type: object
 *                               giftRedemptions:
 *                                 type: array
 *                                 items:
 *                                   $ref: '#/components/schemas/GiftRedemption'
 *                         pagination:
 *                           type: object
 *                           properties:
 *                             page:
 *                               type: integer
 *                             limit:
 *                               type: integer
 *                             total:
 *                               type: integer
 *                             pages:
 *                               type: integer
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /admin/users/{id}:
 *   get:
 *     tags: [Admin]
 *     summary: Get user details
 *     description: Get detailed information about a specific user including progress, buddy, and gift redemptions
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User details retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         phone:
 *                           type: string
 *                           example: "09067509782"
 *                         fullName:
 *                           type: string
 *                           example: "Peterson Okopeterson"
 *                         points:
 *                           type: integer
 *                           example: 1000
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                         location:
 *                           type: string
 *                           example: "Gwagwalada, Abuja"
 *                         status:
 *                           type: string
 *                           example: "Assigned"
 *                         courseLevel:
 *                           type: string
 *                           example: "Stage 3"
 *                     stats:
 *                       type: object
 *                       properties:
 *                         completedStages:
 *                           type: integer
 *                           example: 3
 *                         totalStages:
 *                           type: integer
 *                           example: 10
 *                         progressPercentage:
 *                           type: integer
 *                           example: 30
 *                         totalPointsEarned:
 *                           type: integer
 *                           example: 3000
 *                         currentPoints:
 *                           type: integer
 *                           example: 1000
 *                         pendingGifts:
 *                           type: integer
 *                           example: 0
 *                         redeemedGifts:
 *                           type: integer
 *                           example: 2
 *                     buddy:
 *                       type: object
 *                       properties:
 *                         assigned:
 *                           type: boolean
 *                           example: true
 *                         buddy:
 *                           type: object
 *                           nullable: true
 *                           properties:
 *                             id:
 *                               type: string
 *                             fullName:
 *                               type: string
 *                               example: "Daniel Oyinkasolaoiu"
 *                             phone:
 *                               type: string
 *                               example: "09067509782"
 *                     levelsCompleted:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           stageId:
 *                             type: string
 *                           title:
 *                             type: string
 *                           order:
 *                             type: integer
 *                           score:
 *                             type: integer
 *                           pointsEarned:
 *                             type: integer
 *                           completedAt:
 *                             type: string
 *                             format: date-time
 *                           courseLevel:
 *                             type: string
 *                     giftRedemptions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/GiftRedemption'
 *                     recentTransactions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           points:
 *                             type: integer
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

