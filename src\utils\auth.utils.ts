import bcrypt from 'bcryptjs';
import crypto from 'crypto';

/**
 * Hash a password or PIN
 */
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
};

/**
 * Compare password with hash
 */
export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

/**
 * Generate a random OTP
 */
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Generate a unique user code for gift redemption
 */
export const generateUserCode = (): string => {
  return crypto.randomBytes(4).toString('hex').toUpperCase();
};

/**
 * Normalize phone number to international format
 */
export const normalizePhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  // Handle different formats
  if (digits.startsWith('234')) {
    return `+${digits}`;
  } else if (digits.startsWith('0')) {
    return `+234${digits.substring(1)}`;
  } else if (digits.length === 10) {
    return `+234${digits}`;
  }

  return `+${digits}`;
};

/**
 * Calculate quiz score percentage (legacy - multiple choice)
 */
export const calculateQuizScore = (
  userAnswers: Array<{ questionId: string; selectedAnswers: number[] }>,
  correctAnswers: Array<{ questionId: string; correctAnswers: number[] }>
): number => {
  if (userAnswers.length === 0 || correctAnswers.length === 0) {
    return 0;
  }

  let correctCount = 0;

  userAnswers.forEach(userAnswer => {
    const correctAnswer = correctAnswers.find(ca => ca.questionId === userAnswer.questionId);
    if (correctAnswer) {
      // Check if user's answers match correct answers exactly
      const userSet = new Set(userAnswer.selectedAnswers.sort());
      const correctSet = new Set(correctAnswer.correctAnswers.sort());

      if (userSet.size === correctSet.size &&
          [...userSet].every(answer => correctSet.has(answer))) {
        correctCount++;
      }
    }
  });

  return Math.round((correctCount / userAnswers.length) * 100);
};

/**
 * Calculate quiz score percentage for single choice questions
 */
export const calculateQuizScoreSingleChoice = (
  userAnswers: Array<{ questionId: string; selectedAnswer: number }>,
  correctAnswers: Array<{ questionId: string; correctAnswer: number }>
): number => {
  if (userAnswers.length === 0 || correctAnswers.length === 0) {
    return 0;
  }

  let correctCount = 0;

  userAnswers.forEach(userAnswer => {
    const correctAnswer = correctAnswers.find(ca => ca.questionId === userAnswer.questionId);
    if (correctAnswer && userAnswer.selectedAnswer === correctAnswer.correctAnswer) {
      correctCount++;
    }
  });

  return Math.round((correctCount / userAnswers.length) * 100);
};
