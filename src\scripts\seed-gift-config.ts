import prisma from '../db/db';
import { setGiftValuePerDiamond } from '../utils/diamond.utils';

/**
 * Seed script to initialize gift value configuration
 */
async function seedGiftConfig() {
  try {
    console.log('🌱 Seeding gift value configuration...');

    // Set default gift value per diamond to 3000 points
    await setGiftValuePerDiamond(3000);

    console.log('✅ Gift value configuration seeded successfully!');
    console.log('   - Gift value per diamond: 3000 points');
    
  } catch (error) {
    console.error('❌ Error seeding gift configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedGiftConfig();
}

export default seedGiftConfig;
