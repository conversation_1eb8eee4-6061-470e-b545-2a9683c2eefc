import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                phone: string;
                fullName: string;
                type: 'user';
            };
            admin?: {
                id: string;
                username: string;
                fullName: string;
                role: string;
                type: 'admin';
            };
        }
    }
}
export declare const generateUserToken: (user: {
    id: string;
    phone: string;
    fullName: string;
}) => string;
export declare const generateAdminToken: (admin: {
    id: string;
    username: string;
    fullName: string;
    role: string;
}) => string;
export declare const authenticateUser: (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const authenticateAdmin: (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const requireAdminRole: (allowedRoles: string[]) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
//# sourceMappingURL=auth.middleware.d.ts.map