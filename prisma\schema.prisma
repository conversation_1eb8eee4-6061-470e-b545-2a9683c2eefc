// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  phone       String   @unique
  fullName    String   // Computed from firstName + lastName
  firstName   String?
  lastName    String?
  gender      String?  // Male, Female, Other
  church      String?  // Church location/branch
  profileImage String? // URL to profile image
  pin         String   // Hashed PIN
  buddyId     String?  @db.ObjectId
  buddy       User?    @relation("UserBuddy", fields: [buddyId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  assignedUsers User[] @relation("UserBuddy")
  points      Int      @default(0) // Total points earned
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  progress    UserProgress[]
  reports     Report[]
  giftRedemptions GiftRedemption[]
  pointsTransactions PointsTransaction[]
  badges      UserBadge[]
  streaks     UserStreak[]
  lessonProgress LessonProgress[]

  @@map("users")
}

model Admin {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  username  String   @unique
  password  String   // Hashed password
  fullName  String
  role      AdminRole @default(ADMIN)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdStages Stage[]
  reports       Report[]
  giftRedemptions GiftRedemption[]

  @@map("admins")
}

// System configuration for gift values
model SystemConfig {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  key         String   @unique // e.g., "GIFT_VALUE_PER_DIAMOND"
  value       String   // Store as string, parse as needed
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}

model Stage {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  preacher    String?  // Preacher name (optional for backward compatibility)
  order       Int      @unique
  isActive    Boolean  @default(true)
  createdById String   @db.ObjectId
  createdBy   Admin    @relation(fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  courseOutlines CourseOutline[]
  quizzes     Quiz[]
  progress    UserProgress[]
  badges      Badge[]

  @@map("stages")
}

model CourseOutline {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  stageId     String   @db.ObjectId
  stage       Stage    @relation(fields: [stageId], references: [id], onDelete: Cascade)
  title       String
  description String?
  videoUrl    String?  // YouTube URL
  videoFile   String?  // Cloudinary URL for uploaded video
  duration    Int?     // Video duration in seconds
  order       Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  lessonProgress LessonProgress[]

  @@map("course_outlines")
}

model Quiz {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  stageId   String   @db.ObjectId
  stage     Stage    @relation(fields: [stageId], references: [id], onDelete: Cascade)
  question  String
  questionType QuestionType @default(SINGLE_CHOICE)
  options   String[] // Array of answer options
  correctAnswer Int // Single correct answer index
  points    Int      // Points awarded for correct answer (set by admin)
  explanation String?
  order     Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("quizzes")
}

model UserProgress {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  stageId     String   @db.ObjectId
  stage       Stage    @relation(fields: [stageId], references: [id], onDelete: Cascade)
  isCompleted Boolean  @default(false)
  score       Int?     // Quiz score percentage
  pointsEarned Int     @default(0) // Points earned from this stage
  attempts    Int      @default(0)
  answers     Json?    // Store user's quiz answers
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([userId, stageId])
  @@map("user_progress")
}

model Report {
  id        String     @id @default(auto()) @map("_id") @db.ObjectId
  userId    String     @db.ObjectId
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  adminId   String     @db.ObjectId
  admin     Admin      @relation(fields: [adminId], references: [id])
  title     String
  content   String
  status    ReportStatus @default(ACTIVE)
  tags      String[]   @default([])
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  @@map("reports")
}

model GiftRedemption {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  adminId     String   @db.ObjectId
  admin       Admin    @relation(fields: [adminId], references: [id])
  milestone   String   // e.g., "Stage 1 Completed" or "5000 Points Reached"
  stageId     String?  @db.ObjectId // Reference to stage if stage-based gift
  pointsEarned Int?    // Points earned from stage completion
  diamondsAwarded Int? // Number of diamonds awarded
  pointsRequired Int?   // Points required for this gift (null for stage-based gifts)
  pointsDeducted Int    @default(0) // Points deducted when redeemed
  isRedeemed  Boolean  @default(false)
  redeemedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("gift_redemptions")
}

model PointsTransaction {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  type        PointsTransactionType
  points      Int      // Positive for earned, negative for deducted
  description String   // e.g., "Quiz completed", "Gift redeemed"
  stageId     String?  @db.ObjectId // Reference to stage if points from quiz
  giftRedemptionId String? @db.ObjectId // Reference to gift redemption if points deducted
  createdAt   DateTime @default(now())

  @@map("points_transactions")
}

model OTPVerification {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  phone     String
  otp       String
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())

  @@map("otp_verifications")
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  MODERATOR
}

enum ReportStatus {
  ACTIVE
  NEEDS_HELP
  COMPLETED
  PAUSED
}

enum PointsTransactionType {
  EARNED
  DEDUCTED
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SINGLE_CHOICE
}

// Badge system
model Badge {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String   @unique // e.g., "Foundation of faith", "Called & Chosen"
  description String
  icon        String   // Icon identifier or URL
  stageId     String?  @db.ObjectId // Badge earned for completing this stage
  stage       Stage?   @relation(fields: [stageId], references: [id])
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userBadges  UserBadge[]

  @@map("badges")
}

model UserBadge {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  badgeId   String   @db.ObjectId
  badge     Badge    @relation(fields: [badgeId], references: [id], onDelete: Cascade)
  earnedAt  DateTime @default(now())
  isShared  Boolean  @default(false) // Track if badge was shared

  @@unique([userId, badgeId]) // User can only earn each badge once
  @@map("user_badges")
}

// Streak tracking
model UserStreak {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  userId        String   @db.ObjectId
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentStreak Int      @default(0) // Current consecutive days
  longestStreak Int      @default(0) // Longest streak achieved
  lastActiveDate DateTime? // Last day user was active
  streakData    Json?    // Store daily activity data {"2024-01-01": true, ...}
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([userId]) // One streak record per user
  @@map("user_streaks")
}

// Individual lesson progress tracking
model LessonProgress {
  id              String        @id @default(auto()) @map("_id") @db.ObjectId
  userId          String        @db.ObjectId
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  courseOutlineId String        @db.ObjectId
  courseOutline   CourseOutline @relation(fields: [courseOutlineId], references: [id], onDelete: Cascade)
  isCompleted     Boolean       @default(false)
  watchTime       Int           @default(0) // Seconds watched
  totalDuration   Int?          // Total video duration in seconds
  completedAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@unique([userId, courseOutlineId]) // User can only have one progress per lesson
  @@map("lesson_progress")
}
