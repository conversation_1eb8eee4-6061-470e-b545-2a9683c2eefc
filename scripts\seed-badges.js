const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const defaultBadges = [
  {
    name: 'Foundation of faith',
    description: 'You did it! Each step you take draws you closer to knowing <PERSON> more deeply. Keep going — there\'s more truth ahead.',
    icon: '🏆', // Can be replaced with actual icon URLs
    stageOrder: 1,
  },
  {
    name: '<PERSON> & Chosen',
    description: 'You did it! Each step you take draws you closer to knowing <PERSON> more deeply. Keep going — there\'s more truth ahead.',
    icon: '🌟',
    stageOrder: 2,
  },
  {
    name: 'The Holy Spirit',
    description: 'You did it! Each step you take draws you closer to knowing <PERSON> more deeply. Keep going — there\'s more truth ahead.',
    icon: '🕊️',
    stageOrder: 3,
  },
  {
    name: 'The power of prayer',
    description: 'You did it! Each step you take draws you closer to knowing <PERSON> more deeply. Keep going — there\'s more truth ahead.',
    icon: '🙏',
    stageOrder: 4,
  },
];

async function seedBadges() {
  try {
    console.log('🌱 Starting badge seeding...');

    // Get all stages to map badges to them
    const stages = await prisma.stage.findMany({
      orderBy: { order: 'asc' },
    });

    console.log(`Found ${stages.length} stages`);

    for (const badgeData of defaultBadges) {
      // Find the stage for this badge
      const stage = stages.find(s => s.order === badgeData.stageOrder);
      
      if (!stage) {
        console.log(`⚠️  No stage found for order ${badgeData.stageOrder}, skipping badge "${badgeData.name}"`);
        continue;
      }

      // Check if badge already exists
      const existingBadge = await prisma.badge.findUnique({
        where: { name: badgeData.name },
      });

      if (existingBadge) {
        console.log(`⏭️  Badge "${badgeData.name}" already exists, skipping...`);
        continue;
      }

      // Create the badge
      const badge = await prisma.badge.create({
        data: {
          name: badgeData.name,
          description: badgeData.description,
          icon: badgeData.icon,
          stageId: stage.id,
        },
      });

      console.log(`✅ Created badge "${badge.name}" for stage "${stage.title}"`);
    }

    console.log('🎉 Badge seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding badges:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedBadges();
