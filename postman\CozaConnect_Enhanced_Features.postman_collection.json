{"info": {"name": "CozaConnect Enhanced Features", "description": "API collection for badges, streaks, and lesson progress features", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{userToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "userToken", "value": "", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}], "item": [{"name": "Badge Management", "item": [{"name": "Get My Badges", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/badges/my-badges", "host": ["{{baseUrl}}"], "path": ["badges", "my-badges"]}}, "response": []}, {"name": "Share Badge", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"badgeId\": \"badge_id_here\",\n  \"platform\": \"facebook\"\n}"}, "url": {"raw": "{{baseUrl}}/badges/share", "host": ["{{baseUrl}}"], "path": ["badges", "share"]}}, "response": []}, {"name": "Create Badge (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Foundation of faith\",\n  \"description\": \"You did it! Each step you take draws you closer to knowing <PERSON> more deeply. Keep going — there's more truth ahead.\",\n  \"icon\": \"🏆\",\n  \"stageId\": \"stage_id_here\"\n}"}, "url": {"raw": "{{baseUrl}}/badges", "host": ["{{baseUrl}}"], "path": ["badges"]}}, "response": []}, {"name": "Get All Badges (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/badges", "host": ["{{baseUrl}}"], "path": ["badges"]}}, "response": []}, {"name": "Update Badge (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Badge Name\",\n  \"description\": \"Updated description\",\n  \"icon\": \"🌟\"\n}"}, "url": {"raw": "{{baseUrl}}/badges/:badgeId", "host": ["{{baseUrl}}"], "path": ["badges", ":badgeId"], "variable": [{"key": "badgeId", "value": "badge_id_here"}]}}, "response": []}]}, {"name": "Streak Management", "item": [{"name": "Get My Streak", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/streaks/my-streak", "host": ["{{baseUrl}}"], "path": ["streaks", "my-streak"]}}, "response": []}, {"name": "Update Streak", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"date\": \"2024-01-15\"\n}"}, "url": {"raw": "{{baseUrl}}/streaks/update", "host": ["{{baseUrl}}"], "path": ["streaks", "update"]}}, "response": []}, {"name": "Get Streak Leaderboard (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/streaks/leaderboard", "host": ["{{baseUrl}}"], "path": ["streaks", "leaderboard"]}}, "response": []}]}, {"name": "Lesson Progress", "item": [{"name": "Get Lesson Progress for Stage", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/lessons/progress/:stageId", "host": ["{{baseUrl}}"], "path": ["lessons", "progress", ":stageId"], "variable": [{"key": "stageId", "value": "stage_id_here"}]}}, "response": []}, {"name": "Update Lesson Progress", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"courseOutlineId\": \"course_outline_id_here\",\n  \"watchTime\": 120,\n  \"isCompleted\": true\n}"}, "url": {"raw": "{{baseUrl}}/lessons/progress", "host": ["{{baseUrl}}"], "path": ["lessons", "progress"]}}, "response": []}, {"name": "Bulk Update Lesson Progress", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"lessons\": [\n    {\n      \"courseOutlineId\": \"course_outline_1_id\",\n      \"watchTime\": 120,\n      \"isCompleted\": true\n    },\n    {\n      \"courseOutlineId\": \"course_outline_2_id\",\n      \"watchTime\": 90,\n      \"isCompleted\": false\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/lessons/progress/bulk", "host": ["{{baseUrl}}"], "path": ["lessons", "progress", "bulk"]}}, "response": []}, {"name": "Get Admin Lesson Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/lessons/admin/progress?userId=user_id&stageId=stage_id", "host": ["{{baseUrl}}"], "path": ["lessons", "admin", "progress"], "query": [{"key": "userId", "value": "user_id", "description": "Optional: Filter by specific user"}, {"key": "stageId", "value": "stage_id", "description": "Optional: Filter by specific stage"}]}}, "response": []}]}, {"name": "Enhanced Stage Completion", "item": [{"name": "Complete Stage (Enhanced)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"stageId\": \"stage_id_here\",\n  \"answers\": [\n    {\n      \"questionId\": \"quiz_question_1_id\",\n      \"selectedAnswers\": [0]\n    },\n    {\n      \"questionId\": \"quiz_question_2_id\",\n      \"selectedAnswers\": [1, 2]\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/stages/complete", "host": ["{{baseUrl}}"], "path": ["stages", "complete"]}}, "response": []}]}]}