import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError, sendServerError } from '../utils/response.utils';
import { hashPassword, comparePassword, generateUserCode } from '../utils/auth.utils';
import { uploadToCloudinary } from '../config/cloudinary.config';
import { generateAdminToken } from '../middleware/auth.middleware';
import {
  AdminLoginRequest,
  CreateAdminRequest,
  AssignBuddyRequest,
  CreateReportRequest,
  SearchUserByPhoneRequest,
  InitiateGiftRedemptionRequest,
} from '../schemas/admin.schema';
import {
  CreateStageRequest,
  UpdateStageRequest,
  CreateCourseOutlineRequest,
  UpdateCourseOutlineRequest,
  CreateQuizRequest,
  UpdateQuizRequest,
  PublishStageRequest,
  ReorderItemsRequest,
} from '../schemas/stage.schema';
import { getGiftValueConfig, setGiftValuePerDiamond } from '../utils/diamond.utils';

/**
 * Admin login
 */
export const adminLogin = async (req: Request, res: Response) => {
  try {
    const { username, password }: AdminLoginRequest = req.body;

    // Find admin
    const admin = await prisma.admin.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        password: true,
        fullName: true,
        role: true,
        isActive: true,
      },
    });

    if (!admin || !admin.isActive) {
      return sendError(res, 'Invalid username or password', 401);
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, admin.password);
    if (!isPasswordValid) {
      return sendError(res, 'Invalid username or password', 401);
    }

    // Generate token
    const token = generateAdminToken({
      id: admin.id,
      username: admin.username,
      fullName: admin.fullName,
      role: admin.role,
    });

    return sendSuccess(res, 'Login successful', {
      admin: {
        id: admin.id,
        username: admin.username,
        fullName: admin.fullName,
        role: admin.role,
      },
      token,
    });
  } catch (error) {
    console.error('Admin login error:', error);
    return sendServerError(res, 'Login failed');
  }
};

/**
 * Create new admin (Super Admin only)
 */
export const createAdmin = async (req: Request, res: Response) => {
  try {
    const { username, password, fullName, role }: CreateAdminRequest = req.body;

    // Check if username already exists
    const existingAdmin = await prisma.admin.findUnique({
      where: { username },
    });

    if (existingAdmin) {
      return sendError(res, 'Username already exists', 409);
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create admin
    const admin = await prisma.admin.create({
      data: {
        username,
        password: hashedPassword,
        fullName,
        role,
      },
      select: {
        id: true,
        username: true,
        fullName: true,
        role: true,
        createdAt: true,
      },
    });

    return sendSuccess(res, 'Admin created successfully', admin, 201);
  } catch (error) {
    console.error('Create admin error:', error);
    return sendServerError(res, 'Failed to create admin');
  }
};

/**
 * Get all users with their progress
 */
export const getUsers = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, search } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereClause: any = { isActive: true };

    if (search) {
      whereClause.OR = [
        { fullName: { contains: search as string, mode: 'insensitive' } },
        { phone: { contains: search as string } },
      ];
    }

    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        select: {
          id: true,
          phone: true,
          fullName: true,
          points: true,
          createdAt: true,
          buddy: {
            select: {
              id: true,
              fullName: true,
              phone: true,
            },
          },
          progress: {
            select: {
              stageId: true,
              isCompleted: true,
              score: true,
              pointsEarned: true,
              stage: {
                select: {
                  title: true,
                  order: true,
                },
              },
            },
            orderBy: {
              stage: {
                order: 'asc',
              },
            },
          },
          giftRedemptions: {
            select: {
              id: true,
              milestone: true,
              isRedeemed: true,
              stageId: true,
              pointsRequired: true,
              diamondsAwarded: true,
              pointsEarned: true,
              createdAt: true,
            },
          },
        },
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count({ where: whereClause }),
    ]);

    // Calculate progress statistics and diamonds for each user
    const usersWithStats = await Promise.all(users.map(async (user) => {
      const completedStages = user.progress.filter(p => p.isCompleted).length;
      const totalStages = user.progress.length;
      const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;

      // Get fresh diamonds summary for each user
      const { getUserDiamondsSummary } = await import('../utils/diamond.utils');
      const diamondsSummary = await getUserDiamondsSummary(user.id);

      // Separate stage-based and point-based gifts
      const stageGifts = user.giftRedemptions.filter(g => !g.isRedeemed && g.stageId);
      const pointGifts = user.giftRedemptions.filter(g => !g.isRedeemed && !g.stageId);

      return {
        ...user,
        stats: {
          completedStages,
          totalStages,
          progressPercentage,
          pendingGifts: user.giftRedemptions.filter(g => !g.isRedeemed).length,
          totalDiamonds: diamondsSummary.totalDiamonds,
          availableDiamonds: diamondsSummary.availableDiamonds,
          redeemedDiamonds: diamondsSummary.redeemedDiamonds,
          pendingStageGifts: stageGifts.length,
          pendingPointGifts: pointGifts.length,
        },
      };
    }));

    return sendSuccess(res, 'Users retrieved successfully', {
      users: usersWithStats,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / Number(limit)),
      },
    });
  } catch (error) {
    console.error('Get users error:', error);
    return sendServerError(res, 'Failed to get users');
  }
};

/**
 * Get dashboard overview statistics
 */
export const getDashboardOverview = async (req: Request, res: Response) => {
  try {
    const { days = 7 } = req.query;
    const daysCount = Number(days);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysCount);

    // Get total users count
    const totalUsers = await prisma.user.count({
      where: { isActive: true },
    });

    // Get new users in the specified period
    const newUsers = await prisma.user.count({
      where: {
        isActive: true,
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Get total points in system
    const totalPointsResult = await prisma.user.aggregate({
      where: { isActive: true },
      _sum: {
        points: true,
      },
    });
    const totalPointsInSystem = totalPointsResult._sum.points || 0;

    // Get total diamonds awarded
    const totalDiamondsResult = await prisma.giftRedemption.aggregate({
      where: {
        stageId: { not: null },
        diamondsAwarded: { not: null },
      },
      _sum: {
        diamondsAwarded: true,
      },
    });
    const totalDiamondsAwarded = totalDiamondsResult._sum.diamondsAwarded || 0;

    // Get redeemed diamonds
    const redeemedDiamondsResult = await prisma.giftRedemption.aggregate({
      where: {
        stageId: { not: null },
        diamondsAwarded: { not: null },
        isRedeemed: true,
      },
      _sum: {
        diamondsAwarded: true,
      },
    });
    const redeemedDiamonds = redeemedDiamondsResult._sum.diamondsAwarded || 0;
    const availableDiamonds = totalDiamondsAwarded - redeemedDiamonds;

    // Get users with progress data for chart
    const dailyStats = [];
    for (let i = daysCount - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const dayUsers = await prisma.user.count({
        where: {
          isActive: true,
          createdAt: {
            gte: date,
            lt: nextDate,
          },
        },
      });

      dailyStats.push({
        date: date.toISOString().split('T')[0],
        users: dayUsers,
        label: `Jun ${String(date.getDate()).padStart(2, '0')}`,
      });
    }

    // Get recent notifications (recent user activities)
    const recentUsers = await prisma.user.findMany({
      where: { isActive: true },
      select: {
        id: true,
        fullName: true,
        createdAt: true,
        progress: {
          select: {
            isCompleted: true,
            completedAt: true,
            stage: {
              select: {
                title: true,
              },
            },
          },
          where: {
            isCompleted: true,
          },
          orderBy: {
            completedAt: 'desc',
          },
          take: 1,
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    const notifications = recentUsers.map(user => {
      const latestProgress = user.progress[0];
      if (latestProgress) {
        return {
          id: user.id,
          name: user.fullName,
          action: `just completed ${latestProgress.stage.title}`,
          time: latestProgress.completedAt,
          type: 'stage_completed',
        };
      } else {
        return {
          id: user.id,
          name: user.fullName,
          action: 'just joined COZA',
          time: user.createdAt,
          type: 'user_joined',
        };
      }
    });

    // Get total stages
    const totalStages = await prisma.stage.count({
      where: { isActive: true },
    });

    // Get completion statistics
    const completionStats = await prisma.userProgress.groupBy({
      by: ['isCompleted'],
      _count: {
        id: true,
      },
    });

    const completedCount = completionStats.find(stat => stat.isCompleted)?._count.id || 0;
    const totalAttempts = completionStats.reduce((sum, stat) => sum + stat._count.id, 0);

    return sendSuccess(res, 'Dashboard overview retrieved successfully', {
      totalUsers,
      newUsers,
      totalStages,
      completionRate: totalAttempts > 0 ? Math.round((completedCount / totalAttempts) * 100) : 0,
      totalPointsInSystem,
      diamonds: {
        totalAwarded: totalDiamondsAwarded,
        redeemed: redeemedDiamonds,
        available: availableDiamonds,
      },
      chartData: dailyStats,
      notifications,
      period: `${daysCount} days`,
    });
  } catch (error) {
    console.error('Get dashboard overview error:', error);
    return sendServerError(res, 'Failed to get dashboard overview');
  }
};

/**
 * Get individual user details
 */
export const getUserDetails = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        buddy: {
          select: {
            id: true,
            fullName: true,
            phone: true,
          },
        },
        assignedUsers: {
          select: {
            id: true,
            fullName: true,
            phone: true,
            points: true,
            progress: {
              select: {
                isCompleted: true,
                stage: {
                  select: {
                    title: true,
                    order: true,
                  },
                },
              },
              orderBy: {
                stage: {
                  order: 'asc',
                },
              },
            },
          },
        },
        progress: {
          select: {
            stageId: true,
            isCompleted: true,
            score: true,
            pointsEarned: true,
            attempts: true,
            completedAt: true,
            stage: {
              select: {
                id: true,
                title: true,
                order: true,
              },
            },
          },
          orderBy: {
            stage: {
              order: 'asc',
            },
          },
        },
        giftRedemptions: {
          select: {
            id: true,
            milestone: true,
            pointsRequired: true,
            pointsDeducted: true,
            isRedeemed: true,
            redeemedAt: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        pointsTransactions: {
          select: {
            type: true,
            points: true,
            description: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
      },
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Calculate user statistics
    const completedStages = user.progress.filter(p => p.isCompleted).length;
    const totalStages = user.progress.length;
    const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;
    const totalPointsEarned = user.progress.reduce((sum, p) => sum + p.pointsEarned, 0);
    const pendingGifts = user.giftRedemptions.filter(g => !g.isRedeemed).length;
    const redeemedGifts = user.giftRedemptions.filter(g => g.isRedeemed).length;

    // Get fresh diamonds summary
    const { getUserDiamondsSummary } = await import('../utils/diamond.utils');
    const diamondsSummary = await getUserDiamondsSummary(user.id);

    // Format progress data for levels completed section
    const levelsCompleted = user.progress
      .filter(p => p.isCompleted)
      .map(p => ({
        stageId: p.stageId,
        title: p.stage.title,
        order: p.stage.order,
        score: p.score,
        pointsEarned: p.pointsEarned,
        completedAt: p.completedAt,
        courseLevel: `Stage ${p.stage.order}`,
      }));

    // Format buddy information
    const buddyInfo = user.assignedUsers.length > 0 ? {
      assigned: true,
      buddy: user.assignedUsers[0], // Assuming one-to-one buddy relationship
    } : {
      assigned: false,
      buddy: null,
    };

    return sendSuccess(res, 'User details retrieved successfully', {
      user: {
        id: user.id,
        phone: user.phone,
        fullName: user.fullName,
        points: user.points,
        createdAt: user.createdAt,
        location: 'Gwagwalada, Abuja', // Default location - could be made dynamic
        status: buddyInfo.assigned ? 'Assigned' : 'Not Assigned',
        courseLevel: completedStages > 0 ? `Stage ${completedStages}` : 'Not Started',
      },
      stats: {
        completedStages,
        totalStages,
        progressPercentage,
        totalPointsEarned,
        currentPoints: user.points,
        pendingGifts,
        redeemedGifts,
        totalDiamonds: diamondsSummary.totalDiamonds,
        availableDiamonds: diamondsSummary.availableDiamonds,
        redeemedDiamonds: diamondsSummary.redeemedDiamonds,
      },
      buddy: buddyInfo,
      levelsCompleted,
      giftRedemptions: user.giftRedemptions,
      recentTransactions: user.pointsTransactions,
    });
  } catch (error) {
    console.error('Get user details error:', error);
    return sendServerError(res, 'Failed to get user details');
  }
};

/**
 * Assign buddy to user
 */
export const assignBuddy = async (req: Request, res: Response) => {
  try {
    const { userId, buddyId }: AssignBuddyRequest = req.body;

    // Verify both users exist
    const [user, buddy] = await Promise.all([
      prisma.user.findUnique({ where: { id: userId } }),
      prisma.user.findUnique({ where: { id: buddyId } }),
    ]);

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    if (!buddy) {
      return sendError(res, 'Buddy not found', 404);
    }

    // Update user's buddy
    await prisma.user.update({
      where: { id: userId },
      data: { buddyId },
    });

    return sendSuccess(res, 'Buddy assigned successfully');
  } catch (error) {
    console.error('Assign buddy error:', error);
    return sendServerError(res, 'Failed to assign buddy');
  }
};

/**
 * Create learning stage
 */
export const createStage = async (req: Request, res: Response) => {
  try {
    const { title, description, preacher, order, courseOutlines, quizzes }: CreateStageRequest = req.body;
    const adminId = req.admin!.id;

    // Check if order already exists
    const existingStage = await prisma.stage.findUnique({
      where: { order },
    });

    if (existingStage) {
      return sendError(res, 'Stage order already exists', 409);
    }

    // Create stage with course outlines and quizzes in a transaction
    const stage = await prisma.$transaction(async (tx) => {
      const newStage = await tx.stage.create({
        data: {
          title,
          description,
          preacher: preacher || 'Unknown Preacher', // Provide default if not specified
          order,
          createdById: adminId,
        },
      });

      // Create course outlines
      const outlineData = courseOutlines.map((outline, index) => ({
        stageId: newStage.id,
        title: outline.title,
        description: outline.description,
        videoUrl: outline.videoUrl,
        videoFile: outline.videoFile,
        order: outline.order || index + 1,
      }));

      await tx.courseOutline.createMany({
        data: outlineData,
      });

      // Create quizzes
      const quizData = quizzes.map((quiz, index) => ({
        stageId: newStage.id,
        question: quiz.question,
        questionType: quiz.questionType || 'SINGLE_CHOICE',
        options: quiz.options,
        correctAnswer: quiz.correctAnswer,
        points: quiz.points,
        explanation: quiz.explanation,
        order: quiz.order || index + 1,
      }));

      await tx.quiz.createMany({
        data: quizData,
      });

      return newStage;
    });

    return sendSuccess(res, 'Stage created successfully', stage, 201);
  } catch (error) {
    console.error('Create stage error:', error);
    return sendServerError(res, 'Failed to create stage');
  }
};

/**
 * Get all stages (Admin view) - Enhanced with statistics
 */
export const getAdminStages = async (req: Request, res: Response) => {
  try {
    // First, let's update any stages with null preacher values
    await prisma.stage.updateMany({
      where: { preacher: null },
      data: { preacher: 'Unknown Preacher' },
    });

    const stages = await prisma.stage.findMany({
      include: {
        courseOutlines: {
          select: {
            id: true,
            title: true,
            order: true,
          },
          orderBy: { order: 'asc' },
        },
        quizzes: {
          select: {
            id: true,
            question: true,
            options: true,
            correctAnswer: true,
            points: true,
            explanation: true,
            order: true,
          },
          orderBy: { order: 'asc' },
        },
        createdBy: {
          select: {
            fullName: true,
            username: true,
          },
        },
        _count: {
          select: {
            progress: {
              where: { isCompleted: true },
            },
          },
        },
      },
      orderBy: { order: 'asc' },
    });

    // Get quit counts for each stage (first-timers who didn't complete)
    const stageIds = stages.map(stage => stage.id);
    const quitCounts = await Promise.all(
      stageIds.map(async (stageId) => {
        const count = await prisma.userProgress.count({
          where: {
            stageId,
            isCompleted: false,
          },
        });
        return { stageId, count };
      })
    );

    const quitCountMap = quitCounts.reduce((acc, { stageId, count }) => {
      acc[stageId] = count;
      return acc;
    }, {} as Record<string, number>);

    // Format stages with enhanced statistics
    const enhancedStages = stages.map((stage) => ({
      id: stage.id,
      title: stage.title,
      description: stage.description,
      preacher: stage.preacher || 'Unknown Preacher', // Handle null preacher values
      order: stage.order,
      isActive: stage.isActive,
      createdAt: stage.createdAt,
      updatedAt: stage.updatedAt,
      createdBy: stage.createdBy,

      // Statistics
      completions: stage._count.progress, // Users who completed
      firstTimers: quitCountMap[stage.id] || 0, // Users who quit at this stage
      level: `Stage ${stage.order}`, // Display level
      coins: stage.quizzes.reduce((sum, quiz) => sum + quiz.points, 0), // Total points available

      // Content counts
      outlineCount: stage.courseOutlines.length,
      quizCount: stage.quizzes.length,

      // Include full content for detailed view
      courseOutlines: stage.courseOutlines,
      quizzes: stage.quizzes,
    }));

    return sendSuccess(res, 'Stages retrieved successfully', enhancedStages);
  } catch (error) {
    console.error('Get admin stages error:', error);
    return sendServerError(res, 'Failed to get stages');
  }
};

/**
 * Update stage
 */
export const updateStage = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData: UpdateStageRequest = req.body;

    const stage = await prisma.stage.findUnique({
      where: { id },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // If order is being updated, check for conflicts
    if (updateData.order && updateData.order !== stage.order) {
      const existingStage = await prisma.stage.findUnique({
        where: { order: updateData.order },
      });

      if (existingStage) {
        return sendError(res, 'Stage order already exists', 409);
      }
    }

    const updatedStage = await prisma.stage.update({
      where: { id },
      data: {
        title: updateData.title,
        description: updateData.description,
        preacher: updateData.preacher,
        order: updateData.order,
      },
    });

    return sendSuccess(res, 'Stage updated successfully', updatedStage);
  } catch (error) {
    console.error('Update stage error:', error);
    return sendServerError(res, 'Failed to update stage');
  }
};

/**
 * Delete stage
 */
export const deleteStage = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const stage = await prisma.stage.findUnique({
      where: { id },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Soft delete by setting isActive to false
    await prisma.stage.update({
      where: { id },
      data: { isActive: false },
    });

    return sendSuccess(res, 'Stage deleted successfully');
  } catch (error) {
    console.error('Delete stage error:', error);
    return sendServerError(res, 'Failed to delete stage');
  }
};

/**
 * Get single stage with full details
 */
export const getStageDetails = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const stage = await prisma.stage.findUnique({
      where: { id },
      include: {
        courseOutlines: {
          orderBy: { order: 'asc' },
        },
        quizzes: {
          orderBy: { order: 'asc' },
        },
        createdBy: {
          select: {
            fullName: true,
            username: true,
          },
        },
        _count: {
          select: {
            progress: {
              where: { isCompleted: true },
            },
          },
        },
      },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Get first-timers count (users who quit at this stage)
    const quitCount = await prisma.userProgress.count({
      where: {
        stageId: id,
        isCompleted: false,
      },
    });

    const stageWithStats = {
      ...stage,
      completions: stage._count.progress,
      quitCount,
      totalPoints: stage.quizzes.reduce((sum, quiz) => sum + quiz.points, 0),
    };

    return sendSuccess(res, 'Stage details retrieved successfully', stageWithStats);
  } catch (error) {
    console.error('Get stage details error:', error);
    return sendServerError(res, 'Failed to get stage details');
  }
};

/**
 * Publish/unpublish stage
 */
export const publishStage = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { isActive }: PublishStageRequest = req.body;

    const stage = await prisma.stage.findUnique({
      where: { id },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    const updatedStage = await prisma.stage.update({
      where: { id },
      data: { isActive },
    });

    const action = isActive ? 'published' : 'unpublished';
    return sendSuccess(res, `Stage ${action} successfully`, updatedStage);
  } catch (error) {
    console.error('Publish stage error:', error);
    return sendServerError(res, 'Failed to publish stage');
  }
};

/**
 * Add course outline to existing stage
 */
export const addCourseOutline = async (req: Request, res: Response) => {
  try {
    const { id: stageId } = req.params;
    const outlineData: CreateCourseOutlineRequest = req.body;

    const stage = await prisma.stage.findUnique({
      where: { id: stageId },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Get the next order number
    const lastOutline = await prisma.courseOutline.findFirst({
      where: { stageId },
      orderBy: { order: 'desc' },
    });

    const nextOrder = lastOutline ? lastOutline.order + 1 : 1;

    const courseOutline = await prisma.courseOutline.create({
      data: {
        stageId,
        title: outlineData.title,
        description: outlineData.description,
        videoUrl: outlineData.videoUrl,
        videoFile: outlineData.videoFile,
        order: outlineData.order || nextOrder,
      },
    });

    return sendSuccess(res, 'Course outline added successfully', courseOutline, 201);
  } catch (error) {
    console.error('Add course outline error:', error);
    return sendServerError(res, 'Failed to add course outline');
  }
};

/**
 * Update course outline
 */
export const updateCourseOutline = async (req: Request, res: Response) => {
  try {
    const { id: stageId, outlineId } = req.params;
    const updateData: UpdateCourseOutlineRequest = req.body;

    const courseOutline = await prisma.courseOutline.findFirst({
      where: {
        id: outlineId,
        stageId,
      },
    });

    if (!courseOutline) {
      return sendError(res, 'Course outline not found', 404);
    }

    const updatedOutline = await prisma.courseOutline.update({
      where: { id: outlineId },
      data: {
        title: updateData.title,
        description: updateData.description,
        videoUrl: updateData.videoUrl,
        videoFile: updateData.videoFile,
        order: updateData.order,
      },
    });

    return sendSuccess(res, 'Course outline updated successfully', updatedOutline);
  } catch (error) {
    console.error('Update course outline error:', error);
    return sendServerError(res, 'Failed to update course outline');
  }
};

/**
 * Delete course outline
 */
export const deleteCourseOutline = async (req: Request, res: Response) => {
  try {
    const { id: stageId, outlineId } = req.params;

    const courseOutline = await prisma.courseOutline.findFirst({
      where: {
        id: outlineId,
        stageId,
      },
    });

    if (!courseOutline) {
      return sendError(res, 'Course outline not found', 404);
    }

    await prisma.courseOutline.delete({
      where: { id: outlineId },
    });

    return sendSuccess(res, 'Course outline deleted successfully');
  } catch (error) {
    console.error('Delete course outline error:', error);
    return sendServerError(res, 'Failed to delete course outline');
  }
};

/**
 * Add quiz to existing stage
 */
export const addQuiz = async (req: Request, res: Response) => {
  try {
    const { id: stageId } = req.params;
    const quizData: CreateQuizRequest = req.body;

    const stage = await prisma.stage.findUnique({
      where: { id: stageId },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Get the next order number
    const lastQuiz = await prisma.quiz.findFirst({
      where: { stageId },
      orderBy: { order: 'desc' },
    });

    const nextOrder = lastQuiz ? lastQuiz.order + 1 : 1;

    const quiz = await prisma.quiz.create({
      data: {
        stageId,
        question: quizData.question,
        questionType: quizData.questionType || 'SINGLE_CHOICE',
        options: quizData.options,
        correctAnswer: quizData.correctAnswer,
        points: quizData.points,
        explanation: quizData.explanation,
        order: quizData.order || nextOrder,
      },
    });

    return sendSuccess(res, 'Quiz added successfully', quiz, 201);
  } catch (error) {
    console.error('Add quiz error:', error);
    return sendServerError(res, 'Failed to add quiz');
  }
};

/**
 * Update quiz
 */
export const updateQuiz = async (req: Request, res: Response) => {
  try {
    const { id: stageId, quizId } = req.params;
    const updateData: UpdateQuizRequest = req.body;

    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        stageId,
      },
    });

    if (!quiz) {
      return sendError(res, 'Quiz not found', 404);
    }

    const updatedQuiz = await prisma.quiz.update({
      where: { id: quizId },
      data: {
        question: updateData.question,
        questionType: updateData.questionType,
        options: updateData.options,
        correctAnswer: updateData.correctAnswer,
        points: updateData.points,
        explanation: updateData.explanation,
        order: updateData.order,
      },
    });

    return sendSuccess(res, 'Quiz updated successfully', updatedQuiz);
  } catch (error) {
    console.error('Update quiz error:', error);
    return sendServerError(res, 'Failed to update quiz');
  }
};

/**
 * Delete quiz
 */
export const deleteQuiz = async (req: Request, res: Response) => {
  try {
    const { id: stageId, quizId } = req.params;

    const quiz = await prisma.quiz.findFirst({
      where: {
        id: quizId,
        stageId,
      },
    });

    if (!quiz) {
      return sendError(res, 'Quiz not found', 404);
    }

    await prisma.quiz.delete({
      where: { id: quizId },
    });

    return sendSuccess(res, 'Quiz deleted successfully');
  } catch (error) {
    console.error('Delete quiz error:', error);
    return sendServerError(res, 'Failed to delete quiz');
  }
};

/**
 * Reorder course outlines
 */
export const reorderCourseOutlines = async (req: Request, res: Response) => {
  try {
    const { id: stageId } = req.params;
    const { items }: ReorderItemsRequest = req.body;

    const stage = await prisma.stage.findUnique({
      where: { id: stageId },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Update orders in a transaction
    await prisma.$transaction(
      items.map(item =>
        prisma.courseOutline.update({
          where: {
            id: item.id,
            stageId, // Ensure outline belongs to this stage
          },
          data: { order: item.order },
        })
      )
    );

    return sendSuccess(res, 'Course outlines reordered successfully');
  } catch (error) {
    console.error('Reorder course outlines error:', error);
    return sendServerError(res, 'Failed to reorder course outlines');
  }
};

/**
 * Reorder quizzes
 */
export const reorderQuizzes = async (req: Request, res: Response) => {
  try {
    const { id: stageId } = req.params;
    const { items }: ReorderItemsRequest = req.body;

    const stage = await prisma.stage.findUnique({
      where: { id: stageId },
    });

    if (!stage) {
      return sendError(res, 'Stage not found', 404);
    }

    // Update orders in a transaction
    await prisma.$transaction(
      items.map(item =>
        prisma.quiz.update({
          where: {
            id: item.id,
            stageId, // Ensure quiz belongs to this stage
          },
          data: { order: item.order },
        })
      )
    );

    return sendSuccess(res, 'Quizzes reordered successfully');
  } catch (error) {
    console.error('Reorder quizzes error:', error);
    return sendServerError(res, 'Failed to reorder quizzes');
  }
};

/**
 * Create report
 */
export const createReport = async (req: Request, res: Response) => {
  try {
    const { userId, title, content, status, tags }: CreateReportRequest = req.body;
    const adminId = req.admin!.id;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    const report = await prisma.report.create({
      data: {
        userId,
        adminId,
        title,
        content,
        status,
        tags,
      },
      include: {
        user: {
          select: {
            fullName: true,
            phone: true,
          },
        },
        admin: {
          select: {
            fullName: true,
            username: true,
          },
        },
      },
    });

    return sendSuccess(res, 'Report created successfully', report, 201);
  } catch (error) {
    console.error('Create report error:', error);
    return sendServerError(res, 'Failed to create report');
  }
};

/**
 * Get all reports
 */
export const getReports = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 20, status, userId } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereClause: any = {};

    if (status) {
      whereClause.status = status;
    }

    if (userId) {
      whereClause.userId = userId;
    }

    const [reports, totalCount] = await Promise.all([
      prisma.report.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              fullName: true,
              phone: true,
            },
          },
          admin: {
            select: {
              fullName: true,
              username: true,
            },
          },
        },
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
      }),
      prisma.report.count({ where: whereClause }),
    ]);

    return sendSuccess(res, 'Reports retrieved successfully', {
      reports,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / Number(limit)),
      },
    });
  } catch (error) {
    console.error('Get reports error:', error);
    return sendServerError(res, 'Failed to get reports');
  }
};



/**
 * Search user by phone number for diamond/gift redemption
 */
export const searchUserByPhone = async (req: Request, res: Response) => {
  try {
    const { phone }: SearchUserByPhoneRequest = req.body;

    // Normalize phone number
    const normalizedPhone = phone.replace(/^(\+234|234|0)/, '0');

    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { phone },
          { phone: normalizedPhone },
          { phone: phone.replace(/^0/, '+234') },
          { phone: phone.replace(/^0/, '234') },
        ],
      },
      include: {
        progress: {
          where: { isCompleted: true },
          include: {
            stage: {
              select: {
                title: true,
                order: true,
              },
            },
          },
          orderBy: { completedAt: 'desc' },
        },
        giftRedemptions: {
          where: { isRedeemed: false },
          select: {
            id: true,
            milestone: true,
            stageId: true,
            pointsEarned: true,
            diamondsAwarded: true,
            pointsRequired: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!user) {
      return sendError(res, 'User not found with this phone number', 404);
    }

    // Get user's diamonds summary
    const { getUserDiamondsSummary } = await import('../utils/diamond.utils');
    const diamondsSummary = await getUserDiamondsSummary(user.id);

    // Calculate user statistics
    const completedStages = user.progress.length;
    const currentStage = user.progress.length > 0 ? user.progress[0].stage.order + 1 : 1;

    // Separate stage-based gifts (diamonds) from point-based gifts
    const stageGifts = user.giftRedemptions.filter(gift => gift.stageId && gift.diamondsAwarded);
    const pointGifts = user.giftRedemptions.filter(gift => !gift.stageId && gift.pointsRequired);

    return sendSuccess(res, 'User found successfully', {
      user: {
        id: user.id,
        fullName: user.fullName,
        phone: user.phone,
        points: user.points,
        completedStages,
        currentStage,
        joinedDate: user.createdAt,
      },
      diamonds: {
        totalDiamonds: diamondsSummary.totalDiamonds,
        redeemedDiamonds: diamondsSummary.redeemedDiamonds,
        availableDiamonds: diamondsSummary.availableDiamonds,
      },
      pendingGifts: {
        stageGifts: stageGifts.map(gift => ({
          id: gift.id,
          milestone: gift.milestone,
          diamondsAwarded: gift.diamondsAwarded,
          pointsEarned: gift.pointsEarned,
          createdAt: gift.createdAt,
        })),
        pointGifts: pointGifts.map(gift => ({
          id: gift.id,
          milestone: gift.milestone,
          pointsRequired: gift.pointsRequired,
          createdAt: gift.createdAt,
        })),
        totalPendingDiamonds: stageGifts.reduce((sum, gift) => sum + (gift.diamondsAwarded || 0), 0),
        totalPendingPoints: pointGifts.reduce((sum, gift) => sum + (gift.pointsRequired || 0), 0),
      },
    });
  } catch (error) {
    console.error('Search user by phone error:', error);
    return sendServerError(res, 'Failed to search user');
  }
};

/**
 * Initiate gift redemption with PIN verification (simplified with automatic diamond recalculation)
 */
export const initiateGiftRedemption = async (req: Request, res: Response) => {
  try {
    const { userId, pointsToRedeem, pin }: InitiateGiftRedemptionRequest = req.body;
    const adminId = req.admin!.id;

    // Get user with PIN verification
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        fullName: true,
        phone: true,
        pin: true,
        points: true,
      },
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Verify PIN
    const bcrypt = require('bcryptjs');
    const isPinValid = await bcrypt.compare(pin, user.pin);
    if (!isPinValid) {
      return sendError(res, 'Invalid PIN', 401);
    }

    // Check if user has enough points
    if (user.points < pointsToRedeem) {
      return sendError(res, `Insufficient points for redemption. Required: ${pointsToRedeem}, Available: ${user.points}`, 400);
    }

    // Process redemption in a transaction
    const result = await prisma.$transaction(async (tx) => {
      const newPointsBalance = user.points - pointsToRedeem;

      // Deduct points from user
      await tx.user.update({
        where: { id: userId },
        data: {
          points: {
            decrement: pointsToRedeem,
          },
        },
      });

      // Create points transaction record
      await tx.pointsTransaction.create({
        data: {
          userId,
          type: 'DEDUCTED',
          points: pointsToRedeem,
          description: `Gift redemption - ${pointsToRedeem} points redeemed`,
        },
      });

      // Create gift redemption record
      const userCode = generateUserCode();
      const redemption = await tx.giftRedemption.create({
        data: {
          userId,
          adminId,
          milestone: `${pointsToRedeem} Points Redeemed - Code: ${userCode}`,
          pointsRequired: pointsToRedeem,
          pointsDeducted: pointsToRedeem,
          isRedeemed: true,
          redeemedAt: new Date(),
        },
      });

      // Recalculate and revoke excess diamonds based on new points balance
      const { recalculateAndRevokeExcessDiamonds } = await import('../utils/diamond.utils');
      const revocationResult = await recalculateAndRevokeExcessDiamonds(
        userId,
        newPointsBalance,
        adminId,
        [redemption.id] // Exclude the current redemption
      );

      return {
        userCode,
        pointsRedeemed: pointsToRedeem,
        newPointsBalance,
        diamondsRevoked: revocationResult.diamondsRevoked,
        revokedGifts: revocationResult.revokedGifts,
      };
    });

    return sendSuccess(res, 'Gift redemption processed successfully', {
      redemption: {
        userCode: result.userCode,
        user: {
          fullName: user.fullName,
          phone: user.phone,
        },
        summary: {
          pointsRedeemed: result.pointsRedeemed,
          remainingPoints: result.newPointsBalance,
          diamondsRevoked: result.diamondsRevoked,
        },
        processedAt: new Date(),
        message: result.diamondsRevoked > 0
          ? `Redemption completed. ${result.pointsRedeemed} points redeemed and ${result.diamondsRevoked} diamond(s) were automatically revoked due to insufficient remaining points.`
          : `Redemption completed successfully. ${result.pointsRedeemed} points redeemed.`,
      },
    });
  } catch (error) {
    console.error('Initiate gift redemption error:', error);
    return sendServerError(res, 'Failed to initiate gift redemption');
  }
};

/**
 * Get course statistics for dashboard
 */
export const getCourseStatistics = async (_req: Request, res: Response) => {
  try {
    const [stages, totalUsers, completedStages, totalPoints] = await Promise.all([
      // Get all stages with completion statistics
      prisma.stage.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              progress: {
                where: { isCompleted: true },
              },
            },
          },
          courseOutlines: {
            select: {
              id: true,
              title: true,
            },
          },
          quizzes: {
            select: {
              id: true,
              points: true,
            },
          },
          createdBy: {
            select: {
              fullName: true,
            },
          },
        },
        orderBy: { order: 'asc' },
      }),
      // Total users count
      prisma.user.count(),
      // Total completed stages
      prisma.userProgress.count({
        where: { isCompleted: true },
      }),
      // Total points awarded
      prisma.pointsTransaction.aggregate({
        where: { type: 'EARNED' },
        _sum: { points: true },
      }),
    ]);

    const stageStatistics = stages.map((stage) => ({
      id: stage.id,
      title: stage.title,
      preacher: stage.createdBy.fullName,
      order: stage.order,
      completions: stage._count.progress,
      outlineCount: stage.courseOutlines.length,
      quizCount: stage.quizzes.length,
      totalPoints: stage.quizzes.reduce((sum, quiz) => sum + quiz.points, 0),
      updatedAt: stage.updatedAt,
    }));

    return sendSuccess(res, 'Course statistics retrieved successfully', {
      stages: stageStatistics,
      overview: {
        totalStages: stages.length,
        totalUsers,
        totalCompletions: completedStages,
        totalPointsAwarded: totalPoints._sum.points || 0,
      },
    });
  } catch (error) {
    console.error('Get course statistics error:', error);
    return sendServerError(res, 'Failed to get course statistics');
  }
};

/**
 * Upload file to Cloudinary
 */
export const uploadFile = async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return sendError(res, 'No file uploaded', 400);
    }

    const { folder = 'coza-connect' } = req.body;

    // Upload file to Cloudinary
    const uploadResult = await uploadToCloudinary(req.file, folder);

    return sendSuccess(res, 'File uploaded successfully', uploadResult);
  } catch (error) {
    console.error('Upload file error:', error);
    return sendServerError(res, 'Failed to upload file');
  }
};

/**
 * Get gift value configuration
 */
export const getGiftValueConfiguration = async (req: Request, res: Response) => {
  try {
    const config = await getGiftValueConfig();

    return sendSuccess(res, 'Gift value configuration retrieved successfully', config);
  } catch (error) {
    console.error('Get gift value config error:', error);
    return sendServerError(res, 'Failed to get gift value configuration');
  }
};

/**
 * Update gift value configuration
 */
export const updateGiftValueConfiguration = async (req: Request, res: Response) => {
  try {
    const { giftValuePerDiamond } = req.body;

    if (!giftValuePerDiamond || giftValuePerDiamond < 1) {
      return sendError(res, 'Gift value per diamond must be a positive number', 400);
    }

    await setGiftValuePerDiamond(giftValuePerDiamond);
    const updatedConfig = await getGiftValueConfig();

    return sendSuccess(res, 'Gift value configuration updated successfully', updatedConfig);
  } catch (error) {
    console.error('Update gift value config error:', error);
    return sendServerError(res, 'Failed to update gift value configuration');
  }
};
