"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteProfileImage = exports.uploadProfileImage = exports.changePin = exports.updateProfile = exports.getProfile = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const profile_schema_1 = require("../schemas/profile.schema");
const auth_utils_1 = require("../utils/auth.utils");
const getProfile = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { includeProgress, includeBadges, includeStreak } = req.query;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const includeOptions = {};
        if (includeProgress === 'true') {
            includeOptions.progress = {
                include: {
                    stage: {
                        select: {
                            id: true,
                            title: true,
                            order: true,
                        },
                    },
                },
                orderBy: {
                    stage: {
                        order: 'asc',
                    },
                },
            };
        }
        if (includeBadges === 'true') {
            includeOptions.badges = {
                include: {
                    badge: {
                        include: {
                            stage: {
                                select: {
                                    id: true,
                                    title: true,
                                    order: true,
                                },
                            },
                        },
                    },
                },
                orderBy: { earnedAt: 'desc' },
            };
        }
        if (includeStreak === 'true') {
            includeOptions.streaks = true;
        }
        const user = await db_1.default.user.findUnique({
            where: { id: userId },
            include: includeOptions,
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        let stats = {};
        if (includeProgress === 'true' && user.progress) {
            const completedStages = user.progress.filter(p => p.isCompleted).length;
            const totalStages = user.progress.length;
            const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;
            stats = {
                ...stats,
                completedStages,
                totalStages,
                progressPercentage,
            };
        }
        if (includeBadges === 'true' && user.badges) {
            stats = {
                ...stats,
                totalBadges: user.badges.length,
            };
        }
        if (includeStreak === 'true' && user.streaks && user.streaks.length > 0) {
            const streak = user.streaks[0];
            stats = {
                ...stats,
                currentStreak: streak.currentStreak,
                longestStreak: streak.longestStreak,
            };
        }
        const profile = {
            id: user.id,
            phone: user.phone,
            fullName: user.fullName,
            firstName: user.firstName,
            lastName: user.lastName,
            gender: user.gender,
            church: user.church,
            profileImage: user.profileImage,
            points: user.points,
            createdAt: user.createdAt,
            stats,
        };
        if (includeProgress === 'true') {
            profile.progress = user.progress;
        }
        if (includeBadges === 'true') {
            profile.badges = user.badges?.map(userBadge => ({
                id: userBadge.badge.id,
                name: userBadge.badge.name,
                description: userBadge.badge.description,
                icon: userBadge.badge.icon,
                stage: userBadge.badge.stage,
                earnedAt: userBadge.earnedAt,
                isShared: userBadge.isShared,
            }));
        }
        if (includeStreak === 'true') {
            profile.streak = user.streaks?.[0] || null;
        }
        return (0, response_utils_1.sendSuccess)(res, 'Profile retrieved successfully', profile);
    }
    catch (error) {
        console.error('Get profile error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve profile');
    }
};
exports.getProfile = getProfile;
const updateProfile = async (req, res) => {
    try {
        const userId = req.user?.id;
        const validatedData = profile_schema_1.updateProfileSchema.parse(req.body);
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const { firstName, lastName, gender, church, profileImage } = validatedData;
        const updateData = {};
        if (firstName !== undefined)
            updateData.firstName = firstName;
        if (lastName !== undefined)
            updateData.lastName = lastName;
        if (gender !== undefined)
            updateData.gender = gender;
        if (church !== undefined)
            updateData.church = church;
        if (profileImage !== undefined)
            updateData.profileImage = profileImage;
        if (firstName !== undefined || lastName !== undefined) {
            const currentUser = await db_1.default.user.findUnique({
                where: { id: userId },
                select: { firstName: true, lastName: true },
            });
            const newFirstName = firstName ?? currentUser?.firstName ?? '';
            const newLastName = lastName ?? currentUser?.lastName ?? '';
            updateData.fullName = `${newFirstName} ${newLastName}`.trim();
        }
        const updatedUser = await db_1.default.user.update({
            where: { id: userId },
            data: updateData,
            select: {
                id: true,
                phone: true,
                fullName: true,
                firstName: true,
                lastName: true,
                gender: true,
                church: true,
                profileImage: true,
                points: true,
                updatedAt: true,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Profile updated successfully', updatedUser);
    }
    catch (error) {
        console.error('Update profile error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to update profile');
    }
};
exports.updateProfile = updateProfile;
const changePin = async (req, res) => {
    try {
        const userId = req.user?.id;
        const validatedData = profile_schema_1.changePinSchema.parse(req.body);
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const { currentPin, newPin } = validatedData;
        const user = await db_1.default.user.findUnique({
            where: { id: userId },
            select: { pin: true },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        const isCurrentPinValid = await (0, auth_utils_1.comparePassword)(currentPin, user.pin);
        if (!isCurrentPinValid) {
            return (0, response_utils_1.sendError)(res, 'Current PIN is incorrect', 400);
        }
        const hashedNewPin = await (0, auth_utils_1.hashPassword)(newPin);
        await db_1.default.user.update({
            where: { id: userId },
            data: { pin: hashedNewPin },
        });
        return (0, response_utils_1.sendSuccess)(res, 'PIN changed successfully');
    }
    catch (error) {
        console.error('Change PIN error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to change PIN');
    }
};
exports.changePin = changePin;
const uploadProfileImage = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        if (!req.file) {
            return (0, response_utils_1.sendError)(res, 'No image file uploaded', 400);
        }
        const imageUrl = req.file.path;
        const updatedUser = await db_1.default.user.update({
            where: { id: userId },
            data: { profileImage: imageUrl },
            select: {
                id: true,
                profileImage: true,
                updatedAt: true,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Profile image uploaded successfully', {
            profileImage: updatedUser.profileImage,
            updatedAt: updatedUser.updatedAt,
        });
    }
    catch (error) {
        console.error('Upload profile image error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to upload profile image');
    }
};
exports.uploadProfileImage = uploadProfileImage;
const deleteProfileImage = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        await db_1.default.user.update({
            where: { id: userId },
            data: { profileImage: null },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Profile image deleted successfully');
    }
    catch (error) {
        console.error('Delete profile image error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to delete profile image');
    }
};
exports.deleteProfileImage = deleteProfileImage;
//# sourceMappingURL=profile.controller.js.map