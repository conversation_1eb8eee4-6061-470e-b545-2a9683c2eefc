import { Router } from 'express';
import {
  adminLogin,
  createAdmin,
  getUsers,
  getUserDetails,
  getDashboardOverview,
  assignBuddy,
  createStage,
  getAdminStages,
  getStageDetails,
  updateStage,
  deleteStage,
  publishStage,
  addCourseOutline,
  updateCourseOutline,
  deleteCourseOutline,
  addQuiz,
  updateQuiz,
  deleteQuiz,
  reorderCourseOutlines,
  reorderQuizzes,
  createReport,
  getReports,
  searchUserByPhone,
  initiateGiftRedemption,
  getCourseStatistics,
  uploadFile,
  getGiftValueConfiguration,
  updateGiftValueConfiguration,
} from '../controllers/admin.controller';
import { validateBody, validateParams } from '../middleware/validation.middleware';
import { authenticateAdmin, requireAdminRole } from '../middleware/auth.middleware';
import {
  adminLoginSchema,
  createAdminSchema,
  assignBuddySchema,
  createReportSchema,
  searchUserByPhoneSchema,
  verifyGiftRedemptionSchema,
  initiateGiftRedemptionSchema,
  updateGiftValueConfigSchema,
} from '../schemas/admin.schema';
import {
  createStageSchema,
  updateStageSchema,
  createCourseOutlineSchema,
  updateCourseOutlineSchema,
  createQuizSchema,
  updateQuizSchema,
  publishStageSchema,
  reorderItemsSchema,
} from '../schemas/stage.schema';
import { z } from 'zod';
import { upload } from '../config/cloudinary.config';

const router = Router();

// Public admin routes
router.post('/login', validateBody(adminLoginSchema), adminLogin);

// Protected admin routes
router.use(authenticateAdmin);

// Dashboard
router.get('/dashboard', getDashboardOverview);

// Admin management (Super Admin only)
router.post('/create', requireAdminRole(['SUPER_ADMIN']), validateBody(createAdminSchema), createAdmin);

// User management
router.get('/users', getUsers);
router.get('/users/:id', validateParams(z.object({ id: z.string().min(1, 'User ID is required') })), getUserDetails);
router.put('/users/assign-buddy', validateBody(assignBuddySchema), assignBuddy);

// Stage management
router.get('/stages', getAdminStages);
router.post('/stages', validateBody(createStageSchema), createStage);

const stageIdSchema = z.object({
  id: z.string().min(1, 'Stage ID is required'),
});

const outlineIdSchema = z.object({
  id: z.string().min(1, 'Stage ID is required'),
  outlineId: z.string().min(1, 'Outline ID is required'),
});

const quizIdSchema = z.object({
  id: z.string().min(1, 'Stage ID is required'),
  quizId: z.string().min(1, 'Quiz ID is required'),
});

// Basic stage operations
router.get('/stages/:id', validateParams(stageIdSchema), getStageDetails);
router.put('/stages/:id', validateParams(stageIdSchema), validateBody(updateStageSchema), updateStage);
router.delete('/stages/:id', validateParams(stageIdSchema), deleteStage);
router.put('/stages/:id/publish', validateParams(stageIdSchema), validateBody(publishStageSchema), publishStage);

// Course outline management
router.post('/stages/:id/outlines', validateParams(stageIdSchema), validateBody(createCourseOutlineSchema), addCourseOutline);
router.put('/stages/:id/outlines/:outlineId', validateParams(outlineIdSchema), validateBody(updateCourseOutlineSchema), updateCourseOutline);
router.delete('/stages/:id/outlines/:outlineId', validateParams(outlineIdSchema), deleteCourseOutline);
router.put('/stages/:id/outlines/reorder', validateParams(stageIdSchema), validateBody(reorderItemsSchema), reorderCourseOutlines);

// Quiz management
router.post('/stages/:id/quizzes', validateParams(stageIdSchema), validateBody(createQuizSchema), addQuiz);
router.put('/stages/:id/quizzes/:quizId', validateParams(quizIdSchema), validateBody(updateQuizSchema), updateQuiz);
router.delete('/stages/:id/quizzes/:quizId', validateParams(quizIdSchema), deleteQuiz);
router.put('/stages/:id/quizzes/reorder', validateParams(stageIdSchema), validateBody(reorderItemsSchema), reorderQuizzes);

// Report management
router.get('/reports', getReports);
router.post('/reports', validateBody(createReportSchema), createReport);

// New gift redemption flow
router.post('/gifts/search-user', validateBody(searchUserByPhoneSchema), searchUserByPhone);
router.post('/gifts/initiate', validateBody(initiateGiftRedemptionSchema), initiateGiftRedemption);

// Course statistics
router.get('/courses/statistics', getCourseStatistics);

// File upload
router.post('/upload', upload.single('file'), uploadFile);

// Gift value configuration
router.get('/config/gift-value', getGiftValueConfiguration);
router.put('/config/gift-value', validateBody(updateGiftValueConfigSchema), updateGiftValueConfiguration);

export default router;
