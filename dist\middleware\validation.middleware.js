"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateParams = exports.validateQuery = exports.validateBody = void 0;
const zod_1 = require("zod");
const response_utils_1 = require("../utils/response.utils");
const validateBody = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.body);
            req.body = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.ZodError) {
                const errors = {};
                error.errors.forEach((err) => {
                    const path = err.path.join('.');
                    if (!errors[path]) {
                        errors[path] = [];
                    }
                    errors[path].push(err.message);
                });
                return (0, response_utils_1.sendValidationError)(res, 'Validation failed', errors);
            }
            return (0, response_utils_1.sendValidationError)(res, 'Invalid request data', { general: ['Invalid request format'] });
        }
    };
};
exports.validateBody = validateBody;
const validateQuery = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.query);
            req.query = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.ZodError) {
                const errors = {};
                error.errors.forEach((err) => {
                    const path = err.path.join('.');
                    if (!errors[path]) {
                        errors[path] = [];
                    }
                    errors[path].push(err.message);
                });
                return (0, response_utils_1.sendValidationError)(res, 'Query validation failed', errors);
            }
            return (0, response_utils_1.sendValidationError)(res, 'Invalid query parameters', { general: ['Invalid query format'] });
        }
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.params);
            req.params = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.ZodError) {
                const errors = {};
                error.errors.forEach((err) => {
                    const path = err.path.join('.');
                    if (!errors[path]) {
                        errors[path] = [];
                    }
                    errors[path].push(err.message);
                });
                return (0, response_utils_1.sendValidationError)(res, 'Parameter validation failed', errors);
            }
            return (0, response_utils_1.sendValidationError)(res, 'Invalid parameters', { general: ['Invalid parameter format'] });
        }
    };
};
exports.validateParams = validateParams;
//# sourceMappingURL=validation.middleware.js.map