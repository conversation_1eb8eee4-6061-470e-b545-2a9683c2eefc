"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forgotPinSchema = exports.loginSchema = exports.registerSchema = exports.verifyOtpSchema = exports.sendOtpSchema = exports.otpSchema = exports.pinSchema = exports.phoneSchema = void 0;
const zod_1 = require("zod");
exports.phoneSchema = zod_1.z.string()
    .regex(/^(\+234|234|0)?[789][01]\d{8}$/, 'Invalid Nigerian phone number format');
exports.pinSchema = zod_1.z.string()
    .min(4, 'PIN must be at least 4 digits')
    .max(5, 'PIN must be at most 5 digits')
    .regex(/^\d+$/, 'PIN must contain only digits');
exports.otpSchema = zod_1.z.string()
    .length(6, 'OTP must be exactly 6 digits')
    .regex(/^\d+$/, 'OTP must contain only digits');
exports.sendOtpSchema = zod_1.z.object({
    phone: exports.phoneSchema,
});
exports.verifyOtpSchema = zod_1.z.object({
    phone: exports.phoneSchema,
    otp: exports.otpSchema,
});
exports.registerSchema = zod_1.z.object({
    phone: exports.phoneSchema,
    fullName: zod_1.z.string()
        .min(2, 'Full name must be at least 2 characters')
        .max(100, 'Full name must be at most 100 characters')
        .trim(),
    pin: exports.pinSchema,
});
exports.loginSchema = zod_1.z.object({
    phone: exports.phoneSchema,
    pin: exports.pinSchema,
});
exports.forgotPinSchema = zod_1.z.object({
    phone: exports.phoneSchema,
    otp: exports.otpSchema,
    newPin: exports.pinSchema,
});
//# sourceMappingURL=auth.schema.js.map