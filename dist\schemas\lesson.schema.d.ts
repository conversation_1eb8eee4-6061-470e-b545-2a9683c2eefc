import { z } from 'zod';
export declare const updateLessonProgressSchema: z.ZodObject<{
    courseOutlineId: z.ZodString;
    watchTime: z.ZodOptional<z.ZodNumber>;
    isCompleted: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    courseOutlineId: string;
    isCompleted?: boolean | undefined;
    watchTime?: number | undefined;
}, {
    courseOutlineId: string;
    isCompleted?: boolean | undefined;
    watchTime?: number | undefined;
}>;
export declare const getLessonProgressSchema: z.ZodObject<{
    stageId: z.ZodOptional<z.ZodString>;
    courseOutlineId: z.ZodOptional<z.ZodString>;
    userId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    userId?: string | undefined;
    stageId?: string | undefined;
    courseOutlineId?: string | undefined;
}, {
    userId?: string | undefined;
    stageId?: string | undefined;
    courseOutlineId?: string | undefined;
}>;
export declare const bulkUpdateLessonProgressSchema: z.ZodObject<{
    lessons: z.ZodArray<z.ZodObject<{
        courseOutlineId: z.ZodString;
        watchTime: z.ZodOptional<z.ZodNumber>;
        isCompleted: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        courseOutlineId: string;
        isCompleted?: boolean | undefined;
        watchTime?: number | undefined;
    }, {
        courseOutlineId: string;
        isCompleted?: boolean | undefined;
        watchTime?: number | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    lessons: {
        courseOutlineId: string;
        isCompleted?: boolean | undefined;
        watchTime?: number | undefined;
    }[];
}, {
    lessons: {
        courseOutlineId: string;
        isCompleted?: boolean | undefined;
        watchTime?: number | undefined;
    }[];
}>;
export type UpdateLessonProgressInput = z.infer<typeof updateLessonProgressSchema>;
export type GetLessonProgressInput = z.infer<typeof getLessonProgressSchema>;
export type BulkUpdateLessonProgressInput = z.infer<typeof bulkUpdateLessonProgressSchema>;
//# sourceMappingURL=lesson.schema.d.ts.map