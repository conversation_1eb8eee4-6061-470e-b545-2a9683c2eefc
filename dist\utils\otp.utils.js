"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OTPService = void 0;
const auth_utils_1 = require("./auth.utils");
let twilioClient = null;
function getTwilioClient() {
    if (!twilioClient) {
        try {
            const twilio = require('twilio');
            twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        }
        catch (error) {
            console.error('Failed to initialize Twilio client:', error);
            console.log('Make sure to install Twilio: npm install twilio');
            return null;
        }
    }
    return twilioClient;
}
class OTPService {
    static async sendOTP(phone, otp) {
        try {
            const client = getTwilioClient();
            if (!client) {
                console.log(`📱 [FALLBACK] Sending OTP ${otp} to ${phone}`);
                console.log('⚠️  Twilio not configured. Install with: npm install twilio');
                return true;
            }
            if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN || !process.env.TWILIO_PHONE_NUMBER) {
                console.log(`📱 [FALLBACK] Sending OTP ${otp} to ${phone}`);
                console.log('⚠️  Twilio credentials not configured in .env file');
                return true;
            }
            console.log(`📱 Sending OTP ${otp} to ${phone} via Twilio...`);
            const message = await client.messages.create({
                body: `Your CozaConnect verification code is: ${otp}. Valid for 10 minutes. Do not share this code with anyone.`,
                from: process.env.TWILIO_PHONE_NUMBER,
                to: phone
            });
            console.log(`✅ SMS sent successfully. Message SID: ${message.sid}`);
            return true;
        }
        catch (error) {
            console.error('Failed to send OTP via Twilio:', error);
            if (error.code) {
                console.error(`Twilio Error Code: ${error.code}`);
                console.error(`Twilio Error Message: ${error.message}`);
            }
            if (process.env.NODE_ENV === 'development') {
                console.log(`📱 [FALLBACK] Would send OTP ${otp} to ${phone}`);
                return true;
            }
            return false;
        }
    }
    static async generateAndSendOTP(phone) {
        const otp = (0, auth_utils_1.generateOTP)();
        const success = await this.sendOTP(phone, otp);
        return { otp, success };
    }
}
exports.OTPService = OTPService;
//# sourceMappingURL=otp.utils.js.map