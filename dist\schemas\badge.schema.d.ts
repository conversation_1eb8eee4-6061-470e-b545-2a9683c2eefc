import { z } from 'zod';
export declare const createBadgeSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodString;
    icon: z.ZodString;
    stageId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    description: string;
    icon: string;
    stageId?: string | undefined;
}, {
    name: string;
    description: string;
    icon: string;
    stageId?: string | undefined;
}>;
export declare const updateBadgeSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    icon: z.ZodOptional<z.ZodString>;
    stageId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    description?: string | undefined;
    stageId?: string | undefined;
    icon?: string | undefined;
}, {
    name?: string | undefined;
    description?: string | undefined;
    stageId?: string | undefined;
    icon?: string | undefined;
}>;
export declare const shareBadgeSchema: z.ZodObject<{
    badgeId: z.ZodString;
    platform: z.ZodOptional<z.ZodEnum<["facebook", "twitter", "instagram", "whatsapp"]>>;
}, "strip", z.ZodTypeAny, {
    badgeId: string;
    platform?: "facebook" | "twitter" | "instagram" | "whatsapp" | undefined;
}, {
    badgeId: string;
    platform?: "facebook" | "twitter" | "instagram" | "whatsapp" | undefined;
}>;
export declare const getBadgesSchema: z.ZodObject<{
    stageId: z.ZodOptional<z.ZodString>;
    isActive: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    isActive?: boolean | undefined;
    stageId?: string | undefined;
}, {
    isActive?: boolean | undefined;
    stageId?: string | undefined;
}>;
export type CreateBadgeInput = z.infer<typeof createBadgeSchema>;
export type UpdateBadgeInput = z.infer<typeof updateBadgeSchema>;
export type ShareBadgeInput = z.infer<typeof shareBadgeSchema>;
export type GetBadgesInput = z.infer<typeof getBadgesSchema>;
//# sourceMappingURL=badge.schema.d.ts.map