import { Response } from 'express';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: Record<string, string[]>;
}

/**
 * Send success response
 */
export const sendSuccess = <T>(
  res: Response,
  message: string,
  data?: T,
  statusCode: number = 200
): Response => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
  };
  
  return res.status(statusCode).json(response);
};

/**
 * Send error response
 */
export const sendError = (
  res: Response,
  message: string,
  statusCode: number = 400,
  error?: string
): Response => {
  const response: ApiResponse = {
    success: false,
    message,
    error,
  };
  
  return res.status(statusCode).json(response);
};

/**
 * Send validation error response
 */
export const sendValidationError = (
  res: Response,
  message: string = 'Validation failed',
  errors: Record<string, string[]>,
  statusCode: number = 400
): Response => {
  const response: ApiResponse = {
    success: false,
    message,
    errors,
  };
  
  return res.status(statusCode).json(response);
};

/**
 * Send server error response
 */
export const sendServerError = (
  res: Response,
  message: string = 'Internal server error',
  error?: string
): Response => {
  console.error('Server Error:', error);
  
  const response: ApiResponse = {
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && { error }),
  };
  
  return res.status(500).json(response);
};
