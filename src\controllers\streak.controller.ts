import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError } from '../utils/response.utils';
import { updateStreakSchema } from '../schemas/streak.schema';

/**
 * Get user's streak information
 */
export const getUserStreak = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    let userStreak = await prisma.userStreak.findUnique({
      where: { userId },
    });

    // Create streak record if it doesn't exist
    if (!userStreak) {
      userStreak = await prisma.userStreak.create({
        data: {
          userId,
          currentStreak: 0,
          longestStreak: 0,
          streakData: {},
        },
      });
    }

    // Parse streak data
    const streakData = userStreak.streakData as Record<string, boolean> || {};

    // Get last 7 days for calendar view
    const today = new Date();
    const last7Days = [];

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      last7Days.push({
        date: dateStr,
        day: date.toLocaleDateString('en-US', { weekday: 'short' }).charAt(0),
        isActive: streakData[dateStr] || false,
      });
    }

    const response = {
      currentStreak: userStreak.currentStreak,
      longestStreak: userStreak.longestStreak,
      lastActiveDate: userStreak.lastActiveDate,
      last7Days,
      totalActiveDays: Object.values(streakData).filter(Boolean).length,
    };

    return sendSuccess(res, 'User streak retrieved successfully', response);
  } catch (error) {
    console.error('Get user streak error:', error);
    return sendError(res, 'Failed to retrieve user streak');
  }
};

/**
 * Update user's streak (called when user completes an activity)
 */
export const updateUserStreak = async (userId: string, activityDate?: string) => {
  try {
    const dateStr = activityDate || new Date().toISOString().split('T')[0];

    let userStreak = await prisma.userStreak.findUnique({
      where: { userId },
    });

    // Create streak record if it doesn't exist
    if (!userStreak) {
      userStreak = await prisma.userStreak.create({
        data: {
          userId,
          currentStreak: 0,
          longestStreak: 0,
          streakData: {},
        },
      });
    }

    const streakData = userStreak.streakData as Record<string, boolean> || {};

    // Don't update if already active today
    if (streakData[dateStr]) {
      return userStreak;
    }

    // Mark today as active
    streakData[dateStr] = true;

    // Calculate current streak
    let currentStreak = 0;
    const today = new Date(dateStr);

    // Count consecutive days backwards from today
    for (let i = 0; i >= -365; i--) { // Check up to 365 days back
      const checkDate = new Date(today);
      checkDate.setDate(checkDate.getDate() + i);
      const checkDateStr = checkDate.toISOString().split('T')[0];

      if (streakData[checkDateStr]) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Update longest streak if current is higher
    const longestStreak = Math.max(userStreak.longestStreak, currentStreak);

    // Update streak record
    const updatedStreak = await prisma.userStreak.update({
      where: { userId },
      data: {
        currentStreak,
        longestStreak,
        lastActiveDate: new Date(dateStr),
        streakData,
      },
    });

    console.log(`Streak updated for user ${userId}: ${currentStreak} days`);
    return updatedStreak;
  } catch (error) {
    console.error('Update user streak error:', error);
    return null;
  }
};

/**
 * Manual streak update endpoint (for testing or admin use)
 */
export const manualUpdateStreak = async (req: Request, res: Response) => {
  try {
    const validatedData = updateStreakSchema.parse(req.body);
    const { date } = validatedData;
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    const updatedStreak = await updateUserStreak(userId, date);

    if (!updatedStreak) {
      return sendError(res, 'Failed to update streak');
    }

    return sendSuccess(res, 'Streak updated successfully', {
      currentStreak: updatedStreak.currentStreak,
      longestStreak: updatedStreak.longestStreak,
      lastActiveDate: updatedStreak.lastActiveDate,
    });
  } catch (error) {
    console.error('Manual update streak error:', error);
    return sendError(res, 'Failed to update streak');
  }
};

/**
 * Get streak leaderboard (Admin view)
 */
export const getStreakLeaderboard = async (req: Request, res: Response) => {
  try {
    const topStreaks = await prisma.userStreak.findMany({
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            phone: true,
          },
        },
      },
      orderBy: [
        { currentStreak: 'desc' },
        { longestStreak: 'desc' },
      ],
      take: 50, // Top 50 users
    });

    const leaderboard = topStreaks.map((streak, index) => ({
      rank: index + 1,
      user: streak.user,
      currentStreak: streak.currentStreak,
      longestStreak: streak.longestStreak,
      lastActiveDate: streak.lastActiveDate,
      totalActiveDays: Object.values(streak.streakData as Record<string, boolean> || {}).filter(Boolean).length,
    }));

    return sendSuccess(res, 'Streak leaderboard retrieved successfully', leaderboard);
  } catch (error) {
    console.error('Get streak leaderboard error:', error);
    return sendError(res, 'Failed to retrieve streak leaderboard');
  }
};
