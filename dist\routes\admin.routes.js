"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const admin_controller_1 = require("../controllers/admin.controller");
const validation_middleware_1 = require("../middleware/validation.middleware");
const auth_middleware_1 = require("../middleware/auth.middleware");
const admin_schema_1 = require("../schemas/admin.schema");
const stage_schema_1 = require("../schemas/stage.schema");
const zod_1 = require("zod");
const cloudinary_config_1 = require("../config/cloudinary.config");
const router = (0, express_1.Router)();
router.post('/login', (0, validation_middleware_1.validateBody)(admin_schema_1.adminLoginSchema), admin_controller_1.adminLogin);
router.use(auth_middleware_1.authenticateAdmin);
router.get('/dashboard', admin_controller_1.getDashboardOverview);
router.post('/create', (0, auth_middleware_1.requireAdminRole)(['SUPER_ADMIN']), (0, validation_middleware_1.validateBody)(admin_schema_1.createAdminSchema), admin_controller_1.createAdmin);
router.get('/users', admin_controller_1.getUsers);
router.get('/users/:id', (0, validation_middleware_1.validateParams)(zod_1.z.object({ id: zod_1.z.string().min(1, 'User ID is required') })), admin_controller_1.getUserDetails);
router.put('/users/assign-buddy', (0, validation_middleware_1.validateBody)(admin_schema_1.assignBuddySchema), admin_controller_1.assignBuddy);
router.get('/stages', admin_controller_1.getAdminStages);
router.post('/stages', (0, validation_middleware_1.validateBody)(stage_schema_1.createStageSchema), admin_controller_1.createStage);
const stageIdSchema = zod_1.z.object({
    id: zod_1.z.string().min(1, 'Stage ID is required'),
});
const outlineIdSchema = zod_1.z.object({
    id: zod_1.z.string().min(1, 'Stage ID is required'),
    outlineId: zod_1.z.string().min(1, 'Outline ID is required'),
});
const quizIdSchema = zod_1.z.object({
    id: zod_1.z.string().min(1, 'Stage ID is required'),
    quizId: zod_1.z.string().min(1, 'Quiz ID is required'),
});
router.get('/stages/:id', (0, validation_middleware_1.validateParams)(stageIdSchema), admin_controller_1.getStageDetails);
router.put('/stages/:id', (0, validation_middleware_1.validateParams)(stageIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.updateStageSchema), admin_controller_1.updateStage);
router.delete('/stages/:id', (0, validation_middleware_1.validateParams)(stageIdSchema), admin_controller_1.deleteStage);
router.put('/stages/:id/publish', (0, validation_middleware_1.validateParams)(stageIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.publishStageSchema), admin_controller_1.publishStage);
router.post('/stages/:id/outlines', (0, validation_middleware_1.validateParams)(stageIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.createCourseOutlineSchema), admin_controller_1.addCourseOutline);
router.put('/stages/:id/outlines/:outlineId', (0, validation_middleware_1.validateParams)(outlineIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.updateCourseOutlineSchema), admin_controller_1.updateCourseOutline);
router.delete('/stages/:id/outlines/:outlineId', (0, validation_middleware_1.validateParams)(outlineIdSchema), admin_controller_1.deleteCourseOutline);
router.put('/stages/:id/outlines/reorder', (0, validation_middleware_1.validateParams)(stageIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.reorderItemsSchema), admin_controller_1.reorderCourseOutlines);
router.post('/stages/:id/quizzes', (0, validation_middleware_1.validateParams)(stageIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.createQuizSchema), admin_controller_1.addQuiz);
router.put('/stages/:id/quizzes/:quizId', (0, validation_middleware_1.validateParams)(quizIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.updateQuizSchema), admin_controller_1.updateQuiz);
router.delete('/stages/:id/quizzes/:quizId', (0, validation_middleware_1.validateParams)(quizIdSchema), admin_controller_1.deleteQuiz);
router.put('/stages/:id/quizzes/reorder', (0, validation_middleware_1.validateParams)(stageIdSchema), (0, validation_middleware_1.validateBody)(stage_schema_1.reorderItemsSchema), admin_controller_1.reorderQuizzes);
router.get('/reports', admin_controller_1.getReports);
router.post('/reports', (0, validation_middleware_1.validateBody)(admin_schema_1.createReportSchema), admin_controller_1.createReport);
router.post('/gifts/search-user', (0, validation_middleware_1.validateBody)(admin_schema_1.searchUserByPhoneSchema), admin_controller_1.searchUserByPhone);
router.post('/gifts/initiate', (0, validation_middleware_1.validateBody)(admin_schema_1.initiateGiftRedemptionSchema), admin_controller_1.initiateGiftRedemption);
router.get('/courses/statistics', admin_controller_1.getCourseStatistics);
router.post('/upload', cloudinary_config_1.upload.single('file'), admin_controller_1.uploadFile);
exports.default = router;
//# sourceMappingURL=admin.routes.js.map