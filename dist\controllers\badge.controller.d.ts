import { Request, Response } from 'express';
export declare const createBadge: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getAllBadges: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUserBadges: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const awardBadge: (userId: string, stageId: string) => Promise<({
    badge: {
        id: string;
        name: string;
        description: string;
        icon: string;
        stageId: string | null;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
    };
} & {
    id: string;
    userId: string;
    badgeId: string;
    earnedAt: Date;
    isShared: boolean;
}) | null>;
export declare const shareBadge: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateBadge: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const deleteBadge: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=badge.controller.d.ts.map