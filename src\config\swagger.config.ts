import swaggerJsdoc from 'swagger-jsdoc';
import { SwaggerDefinition } from 'swagger-jsdoc';

const swaggerDefinition: SwaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'CozaConnect API',
    version: '1.0.0',
    description: `
      CozaConnect is a comprehensive learning platform API built with Node.js, Express, TypeScript, Zod, and Prisma.
      It supports user registration with SMS OTP verification, learning stages with quizzes, gamification features,
      admin management, and gift redemption.

      ## 🚀 Key Features
      - **User Authentication**: SMS OTP verification with JWT tokens
      - **Learning Stages**: Video lessons with quizzes and progress tracking
      - **Gamification**: Points, badges, and streak tracking
      - **Profile Management**: User profiles with image upload
      - **Admin Dashboard**: Complete content and user management
      - **Gift Redemption**: Points-based reward system

      ## 🔐 Authentication
      Most endpoints require JWT authentication. Include the token in the Authorization header:
      \`Authorization: Bearer <your-jwt-token>\`

      Admin endpoints require admin-level authentication.

      ## 📱 Phone Number Format
      Phone numbers should be in Nigerian format:
      - \`+2348012345678\` (preferred)
      - \`2348012345678\`
      - \`08012345678\`

      ## 🎮 Gamification System
      - **Points**: 1000 points per correct quiz answer
      - **Badges**: Earned for completing stages
      - **Streaks**: Daily activity tracking with calendar view
      - **Gifts**: First gift at 5,000 points, then every 50,000 points
      - **Leaderboards**: Streak competitions

      ## 📚 Learning Progress
      - **Individual Lesson Tracking**: Watch time and completion status
      - **Stage Progress**: Overall completion percentages
      - **Automatic Unlocking**: Sequential stage access
      - **Multiple Attempts**: Retake quizzes to improve scores

      ## 👤 Profile Features
      - **Extended Profiles**: First name, last name, gender, church
      - **Profile Images**: Cloudinary-powered image uploads
      - **PIN Management**: Secure 4-digit PIN changes
      - **Activity Stats**: Progress, badges, and streak information
    `,
    contact: {
      name: 'CozaConnect API Support',
      email: '<EMAIL>',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: 'http://localhost:8000/api/v1',
      description: 'Development server',
    },
    {
      url: 'https://coza-connect-production.up.railway.app/api/v1',
      description: 'Production server',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Enter JWT token for user authentication',
      },
      adminAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Enter JWT token for admin authentication',
      },
    },
    schemas: {
      // Common response schemas
      SuccessResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          message: {
            type: 'string',
            example: 'Operation successful',
          },
          data: {
            type: 'object',
            description: 'Response data',
          },
        },
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false,
          },
          message: {
            type: 'string',
            example: 'Error message',
          },
          error: {
            type: 'string',
            description: 'Detailed error (development only)',
          },
          errors: {
            type: 'object',
            description: 'Validation errors',
            additionalProperties: {
              type: 'array',
              items: {
                type: 'string',
              },
            },
          },
        },
      },
      // User schemas
      User: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'User ID',
          },
          phone: {
            type: 'string',
            description: 'User phone number',
            example: '+2348012345678',
          },
          fullName: {
            type: 'string',
            description: 'User full name',
            example: 'John Doe',
          },
          firstName: {
            type: 'string',
            description: 'User first name',
            example: 'John',
          },
          lastName: {
            type: 'string',
            description: 'User last name',
            example: 'Doe',
          },
          gender: {
            type: 'string',
            enum: ['Male', 'Female', 'Other'],
            description: 'User gender',
          },
          church: {
            type: 'string',
            description: 'User church location',
            example: 'Wuse Zone 5',
          },
          profileImage: {
            type: 'string',
            description: 'Profile image URL',
            example: 'https://res.cloudinary.com/example/image/upload/v123/profile.jpg',
          },
          points: {
            type: 'integer',
            description: 'User total points',
            example: 5000,
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'User creation date',
          },
        },
      },
      // Stage schemas
      Stage: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Stage ID',
          },
          title: {
            type: 'string',
            description: 'Stage title',
            example: 'Introduction to Faith',
          },
          description: {
            type: 'string',
            description: 'Stage description',
          },
          videoUrl: {
            type: 'string',
            description: 'Stage video URL',
            example: 'https://youtube.com/watch?v=example',
          },
          order: {
            type: 'integer',
            description: 'Stage order',
            example: 1,
          },
          isUnlocked: {
            type: 'boolean',
            description: 'Whether stage is unlocked for user',
          },
          isCompleted: {
            type: 'boolean',
            description: 'Whether user completed this stage',
          },
          score: {
            type: 'integer',
            description: 'User score for this stage',
            nullable: true,
          },
          pointsEarned: {
            type: 'integer',
            description: 'Points earned from this stage',
            example: 3000,
          },
          attempts: {
            type: 'integer',
            description: 'Number of attempts',
            example: 1,
          },
        },
      },
      // Quiz schemas
      Quiz: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Quiz ID',
          },
          question: {
            type: 'string',
            description: 'Quiz question',
            example: 'What is faith according to Hebrews 11:1?',
          },
          options: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: 'Answer options',
            example: ['Hope for the future', 'Confidence in what we hope for', 'Believing in yourself'],
          },
          explanation: {
            type: 'string',
            description: 'Answer explanation',
          },
          order: {
            type: 'integer',
            description: 'Question order',
            example: 1,
          },
        },
      },
      // Points schemas
      PointsSummary: {
        type: 'object',
        properties: {
          totalPoints: {
            type: 'integer',
            description: 'User total points',
            example: 7500,
          },
          eligibleGifts: {
            type: 'integer',
            description: 'Number of gifts user is eligible for',
            example: 1,
          },
          pointsForNextGift: {
            type: 'integer',
            description: 'Points needed for next gift',
            example: 47500,
          },
          recentTransactions: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/PointsTransaction',
            },
          },
        },
      },
      PointsTransaction: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
            enum: ['EARNED', 'DEDUCTED'],
            description: 'Transaction type',
          },
          points: {
            type: 'integer',
            description: 'Points amount',
            example: 1000,
          },
          description: {
            type: 'string',
            description: 'Transaction description',
            example: 'Quiz completed: Introduction to Faith',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Transaction date',
          },
        },
      },
      // Gift schemas
      GiftRedemption: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Gift redemption ID',
          },
          milestone: {
            type: 'string',
            description: 'Milestone achieved',
            example: 'Stage 1 Completed',
          },
          giftType: {
            type: 'string',
            description: 'Type of gift',
            example: 'T-shirt',
          },
          userCode: {
            type: 'string',
            description: 'Unique verification code',
            example: 'ABC123XYZ',
          },
          pointsRequired: {
            type: 'integer',
            description: 'Points required for this gift',
            nullable: true,
            example: 5000,
          },
          isRedeemed: {
            type: 'boolean',
            description: 'Whether gift has been redeemed',
          },
          redeemedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Redemption date',
            nullable: true,
          },
        },
      },
      // Enhanced Stage schemas
      CourseOutline: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Course outline ID',
          },
          title: {
            type: 'string',
            description: 'Outline title',
            example: 'Introduction to Faith',
          },
          description: {
            type: 'string',
            description: 'Outline description',
          },
          videoUrl: {
            type: 'string',
            description: 'YouTube video URL',
            example: 'https://www.youtube.com/watch?v=example',
          },
          videoFile: {
            type: 'string',
            description: 'Cloudinary video file URL',
          },
          order: {
            type: 'integer',
            description: 'Outline order',
            example: 1,
          },
        },
      },
      EnhancedStage: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Stage ID',
          },
          title: {
            type: 'string',
            description: 'Stage title',
            example: 'Faith Foundations',
          },
          description: {
            type: 'string',
            description: 'Stage description',
          },
          preacher: {
            type: 'string',
            description: 'Preacher name',
            example: 'Rev. Biodun Fatoyinbo',
          },
          order: {
            type: 'integer',
            description: 'Stage order',
            example: 1,
          },
          courseOutlines: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/CourseOutline',
            },
          },
          quizzes: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/EnhancedQuiz',
            },
          },
        },
      },
      EnhancedQuiz: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Quiz ID',
          },
          question: {
            type: 'string',
            description: 'Quiz question',
            example: 'What does it mean to depend on God?',
          },
          questionType: {
            type: 'string',
            enum: ['SINGLE_CHOICE'],
            description: 'Question type (single choice only)',
            example: 'SINGLE_CHOICE',
          },
          options: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: 'Answer options',
            example: ['Getting your opinion from friends', 'Trusting in God with your whole heart'],
          },
          correctAnswer: {
            type: 'integer',
            description: 'Index of the correct answer',
            example: 1,
          },
          points: {
            type: 'integer',
            description: 'Points awarded for correct answer (set by admin)',
            example: 500,
          },
          explanation: {
            type: 'string',
            description: 'Answer explanation',
          },
          order: {
            type: 'integer',
            description: 'Question order',
            example: 1,
          },
        },
      },
      // Gift redemption schemas
      UserSearchResult: {
        type: 'object',
        properties: {
          user: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                description: 'User ID',
              },
              fullName: {
                type: 'string',
                description: 'User full name',
                example: 'Peterson Okopeterson',
              },
              phone: {
                type: 'string',
                description: 'User phone number',
                example: '09067509782',
              },
              points: {
                type: 'integer',
                description: 'User total points',
                example: 16000,
              },
              completedStages: {
                type: 'integer',
                description: 'Number of completed stages',
                example: 3,
              },
              currentStage: {
                type: 'integer',
                description: 'Current stage number',
                example: 4,
              },
              availableGifts: {
                type: 'integer',
                description: 'Number of available gifts',
                example: 2,
              },
            },
          },
          pendingGifts: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/GiftRedemption',
            },
          },
        },
      },
      FileUploadResponse: {
        type: 'object',
        properties: {
          url: {
            type: 'string',
            description: 'Cloudinary file URL',
            example: 'https://res.cloudinary.com/example/video/upload/v123/file.mp4',
          },
          publicId: {
            type: 'string',
            description: 'Cloudinary public ID',
            example: 'coza-connect/video123',
          },
          originalName: {
            type: 'string',
            description: 'Original file name',
            example: 'lesson-video.mp4',
          },
          size: {
            type: 'integer',
            description: 'File size in bytes',
            example: 1024000,
          },
          mimetype: {
            type: 'string',
            description: 'File MIME type',
            example: 'video/mp4',
          },
        },
      },
      // New schemas for enhanced stage management
      AdminCourseOutline: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Course outline ID',
          },
          title: {
            type: 'string',
            description: 'Outline title',
            example: 'Absolute dependence on God',
          },
          description: {
            type: 'string',
            description: 'Outline description',
          },
          videoUrl: {
            type: 'string',
            description: 'YouTube video URL',
            example: 'https://youtube.com/watch?v=example',
          },
          videoFile: {
            type: 'string',
            description: 'Cloudinary video URL',
          },
          order: {
            type: 'integer',
            description: 'Outline order',
            example: 1,
          },
        },
      },
      AdminStageDetails: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Stage ID',
          },
          title: {
            type: 'string',
            description: 'Stage title',
            example: 'Dependence on God',
          },
          description: {
            type: 'string',
            description: 'Stage description',
          },
          preacher: {
            type: 'string',
            description: 'Preacher name',
            example: 'Rev. Biodun Fatoyinbo',
          },
          order: {
            type: 'integer',
            description: 'Stage order',
            example: 1,
          },
          isActive: {
            type: 'boolean',
            description: 'Whether stage is active/published',
          },
          completions: {
            type: 'integer',
            description: 'Number of users who completed this stage',
            example: 1287,
          },
          firstTimers: {
            type: 'integer',
            description: 'Number of users who quit at this stage',
            example: 10,
          },
          level: {
            type: 'string',
            description: 'Display level',
            example: 'Stage 1',
          },
          coins: {
            type: 'integer',
            description: 'Total points available in this stage',
            example: 12000,
          },
          outlineCount: {
            type: 'integer',
            description: 'Number of course outlines',
            example: 2,
          },
          quizCount: {
            type: 'integer',
            description: 'Number of quiz questions',
            example: 5,
          },
          courseOutlines: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/AdminCourseOutline',
            },
          },
          quizzes: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Quiz',
            },
          },
          createdBy: {
            type: 'object',
            properties: {
              fullName: {
                type: 'string',
                example: 'Admin User',
              },
              username: {
                type: 'string',
                example: 'admin',
              },
            },
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
          },
        },
      },
      ReorderItems: {
        type: 'object',
        properties: {
          items: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'Item ID',
                },
                order: {
                  type: 'integer',
                  description: 'New order position',
                  example: 1,
                },
              },
              required: ['id', 'order'],
            },
          },
        },
        required: ['items'],
      },
      PublishStage: {
        type: 'object',
        properties: {
          isActive: {
            type: 'boolean',
            description: 'Whether to publish (true) or unpublish (false) the stage',
            example: true,
          },
        },
        required: ['isActive'],
      },
      // Badge System Schemas
      Badge: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Badge ID',
          },
          name: {
            type: 'string',
            description: 'Badge name',
            example: 'Foundation of faith',
          },
          description: {
            type: 'string',
            description: 'Badge description',
            example: 'You did it! Each step you take draws you closer to knowing Christ more deeply.',
          },
          icon: {
            type: 'string',
            description: 'Badge icon (emoji or URL)',
            example: '🏆',
          },
          stage: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
              order: { type: 'integer' },
            },
          },
          earnedAt: {
            type: 'string',
            format: 'date-time',
            description: 'When the badge was earned',
          },
          isShared: {
            type: 'boolean',
            description: 'Whether the badge has been shared',
          },
        },
      },
      UserBadge: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'User badge ID',
          },
          badge: {
            $ref: '#/components/schemas/Badge',
          },
          earnedAt: {
            type: 'string',
            format: 'date-time',
            description: 'When the badge was earned',
          },
          isShared: {
            type: 'boolean',
            description: 'Whether the badge has been shared',
          },
        },
      },
      // Streak System Schemas
      UserStreak: {
        type: 'object',
        properties: {
          currentStreak: {
            type: 'integer',
            description: 'Current consecutive days streak',
            example: 7,
          },
          longestStreak: {
            type: 'integer',
            description: 'Longest streak achieved',
            example: 15,
          },
          lastActiveDate: {
            type: 'string',
            format: 'date-time',
            description: 'Last day user was active',
          },
          last7Days: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                date: { type: 'string', format: 'date' },
                day: { type: 'string', example: 'M' },
                isActive: { type: 'boolean' },
              },
            },
            description: 'Last 7 days activity calendar',
          },
          totalActiveDays: {
            type: 'integer',
            description: 'Total number of active days',
            example: 25,
          },
        },
      },
      StreakLeaderboard: {
        type: 'object',
        properties: {
          rank: {
            type: 'integer',
            description: 'User rank in leaderboard',
            example: 1,
          },
          user: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              fullName: { type: 'string' },
              phone: { type: 'string' },
            },
          },
          currentStreak: {
            type: 'integer',
            description: 'Current streak',
            example: 15,
          },
          longestStreak: {
            type: 'integer',
            description: 'Longest streak',
            example: 20,
          },
          totalActiveDays: {
            type: 'integer',
            description: 'Total active days',
            example: 45,
          },
        },
      },
      // Lesson Progress Schemas
      LessonProgress: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Lesson ID',
          },
          title: {
            type: 'string',
            description: 'Lesson title',
            example: 'Introduction to Faith',
          },
          description: {
            type: 'string',
            description: 'Lesson description',
          },
          videoUrl: {
            type: 'string',
            description: 'YouTube video URL',
          },
          videoFile: {
            type: 'string',
            description: 'Cloudinary video URL',
          },
          duration: {
            type: 'integer',
            description: 'Video duration in seconds',
            example: 1800,
          },
          order: {
            type: 'integer',
            description: 'Lesson order',
            example: 1,
          },
          progress: {
            type: 'object',
            properties: {
              isCompleted: { type: 'boolean' },
              watchTime: { type: 'integer', description: 'Seconds watched' },
              completedAt: { type: 'string', format: 'date-time', nullable: true },
              progressPercentage: { type: 'integer', example: 75 },
            },
          },
        },
      },
      StageProgress: {
        type: 'object',
        properties: {
          stage: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
              description: { type: 'string' },
              order: { type: 'integer' },
            },
          },
          lessons: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/LessonProgress',
            },
          },
          summary: {
            type: 'object',
            properties: {
              totalLessons: { type: 'integer' },
              completedLessons: { type: 'integer' },
              progressPercentage: { type: 'integer' },
            },
          },
        },
      },
      // Profile Management Schemas
      ProfileUpdate: {
        type: 'object',
        properties: {
          firstName: {
            type: 'string',
            description: 'First name',
            example: 'John',
          },
          lastName: {
            type: 'string',
            description: 'Last name',
            example: 'Doe',
          },
          gender: {
            type: 'string',
            enum: ['Male', 'Female', 'Other'],
            description: 'Gender',
          },
          church: {
            type: 'string',
            description: 'Church location',
            example: 'Wuse Zone 5',
          },
          profileImage: {
            type: 'string',
            description: 'Profile image URL',
          },
        },
      },
      PinChange: {
        type: 'object',
        properties: {
          currentPin: {
            type: 'string',
            description: 'Current 4-digit PIN',
            example: '1234',
            pattern: '^\\d{4}$',
          },
          newPin: {
            type: 'string',
            description: 'New 4-digit PIN',
            example: '5678',
            pattern: '^\\d{4}$',
          },
          confirmPin: {
            type: 'string',
            description: 'Confirm new PIN',
            example: '5678',
            pattern: '^\\d{4}$',
          },
        },
        required: ['currentPin', 'newPin', 'confirmPin'],
      },
      ProfileWithStats: {
        type: 'object',
        allOf: [
          { $ref: '#/components/schemas/User' },
          {
            type: 'object',
            properties: {
              stats: {
                type: 'object',
                properties: {
                  completedStages: { type: 'integer' },
                  totalStages: { type: 'integer' },
                  progressPercentage: { type: 'integer' },
                  totalBadges: { type: 'integer' },
                  currentStreak: { type: 'integer' },
                  longestStreak: { type: 'integer' },
                },
              },
              progress: {
                type: 'array',
                items: {
                  $ref: '#/components/schemas/Stage',
                },
              },
              badges: {
                type: 'array',
                items: {
                  $ref: '#/components/schemas/Badge',
                },
              },
              streak: {
                $ref: '#/components/schemas/UserStreak',
              },
            },
          },
        ],
      },
      // Enhanced Stage Completion Response
      StageCompletionResponse: {
        type: 'object',
        properties: {
          score: {
            type: 'integer',
            description: 'Quiz score percentage',
            example: 80,
          },
          pointsEarned: {
            type: 'integer',
            description: 'Points earned from this completion',
            example: 3000,
          },
          isCompleted: {
            type: 'boolean',
            description: 'Whether the stage was completed (score >= 60%)',
          },
          attempts: {
            type: 'integer',
            description: 'Number of attempts for this stage',
            example: 1,
          },
          pointsSummary: {
            $ref: '#/components/schemas/PointsSummary',
          },
          newGiftsEarned: {
            type: 'integer',
            description: 'Number of new gifts earned',
            example: 1,
          },
          badgeEarned: {
            type: 'object',
            nullable: true,
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' },
              icon: { type: 'string' },
            },
          },
          message: {
            type: 'string',
            description: 'Success or failure message',
          },
        },
      },
    },
    responses: {
      UnauthorizedError: {
        description: 'Authentication required',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      ForbiddenError: {
        description: 'Insufficient permissions',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      ValidationError: {
        description: 'Validation error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      ServerError: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
    },
  },
  tags: [
    {
      name: 'Authentication',
      description: 'User authentication endpoints',
    },
    {
      name: 'Profile',
      description: 'User profile management endpoints',
    },
    {
      name: 'Stages',
      description: 'Learning stages and quiz endpoints',
    },
    {
      name: 'Lessons',
      description: 'Individual lesson progress tracking endpoints',
    },
    {
      name: 'Badges',
      description: 'Badge system and gamification endpoints',
    },
    {
      name: 'Streaks',
      description: 'Daily activity streak tracking endpoints',
    },
    {
      name: 'Admin',
      description: 'Admin management endpoints',
    },
    {
      name: 'Health',
      description: 'Health check endpoints',
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/docs/*.ts',
  ],
};

export const swaggerSpec = swaggerJsdoc(options);
