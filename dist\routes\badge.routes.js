"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const badge_controller_1 = require("../controllers/badge.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.get('/my-badges', auth_middleware_1.authenticateUser, badge_controller_1.getUserBadges);
router.post('/share', auth_middleware_1.authenticateUser, badge_controller_1.shareBadge);
router.post('/', auth_middleware_1.authenticateAdmin, badge_controller_1.createBadge);
router.get('/', auth_middleware_1.authenticateAdmin, badge_controller_1.getAllBadges);
router.put('/:id', auth_middleware_1.authenticateAdmin, badge_controller_1.updateBadge);
router.delete('/:id', auth_middleware_1.authenticateAdmin, badge_controller_1.deleteBadge);
exports.default = router;
//# sourceMappingURL=badge.routes.js.map