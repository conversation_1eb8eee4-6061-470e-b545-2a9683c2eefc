import { z } from 'zod';

// Quiz answer submission
export const quizAnswerSchema = z.object({
  questionId: z.string().min(1, 'Question ID is required'),
  selectedAnswers: z.array(z.number().int().min(0), {
    required_error: 'Selected answers are required',
  }).min(1, 'At least one answer must be selected'),
});

// Complete stage request
export const completeStageSchema = z.object({
  stageId: z.string().min(1, 'Stage ID is required'),
  answers: z.array(quizAnswerSchema).min(1, 'Quiz answers are required'),
});

// Course outline item schema
export const courseOutlineSchema = z.object({
  title: z.string().min(1, 'Outline title is required'),
  description: z.string().optional(),
  videoUrl: z.string().url('Invalid video URL').optional(),
  videoFile: z.string().optional(), // Cloudinary URL for uploaded video
  order: z.number().int().min(1, 'Order must be positive'),
});

// Quiz schema with points
export const quizSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  questionType: z.enum(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SINGLE_CHOICE']).default('MULTIPLE_CHOICE'),
  options: z.array(z.string().min(1, 'Option cannot be empty')).min(2, 'At least 2 options required'),
  correctAnswers: z.array(z.number().int().min(0)).min(1, 'At least one correct answer required'),
  points: z.number().int().min(1, 'Points must be positive').default(500),
  explanation: z.string().optional(),
  order: z.number().int().min(1, 'Question order must be positive'),
});

// Create stage (Admin) - Enhanced
export const createStageSchema = z.object({
  title: z.string().min(1, 'Stage title is required').max(200, 'Title too long'),
  description: z.string().optional(),
  preacher: z.string().min(1, 'Preacher name is required').optional(),
  order: z.number().int().min(1, 'Order must be a positive integer'),
  courseOutlines: z.array(courseOutlineSchema).min(1, 'At least one course outline is required'),
  quizzes: z.array(quizSchema).min(1, 'At least one quiz question is required'),
});

// Update stage (Admin)
export const updateStageSchema = createStageSchema.partial();

// Individual course outline management
export const createCourseOutlineSchema = courseOutlineSchema;
export const updateCourseOutlineSchema = courseOutlineSchema.partial();

// Individual quiz management
export const createQuizSchema = quizSchema;
export const updateQuizSchema = quizSchema.partial();

// Stage publishing
export const publishStageSchema = z.object({
  isActive: z.boolean(),
});

// Reorder items
export const reorderItemsSchema = z.object({
  items: z.array(z.object({
    id: z.string().min(1, 'Item ID is required'),
    order: z.number().int().min(1, 'Order must be positive'),
  })).min(1, 'At least one item is required'),
});

export type QuizAnswer = z.infer<typeof quizAnswerSchema>;
export type CompleteStageRequest = z.infer<typeof completeStageSchema>;
export type CourseOutline = z.infer<typeof courseOutlineSchema>;
export type Quiz = z.infer<typeof quizSchema>;
export type CreateStageRequest = z.infer<typeof createStageSchema>;
export type UpdateStageRequest = z.infer<typeof updateStageSchema>;
export type CreateCourseOutlineRequest = z.infer<typeof createCourseOutlineSchema>;
export type UpdateCourseOutlineRequest = z.infer<typeof updateCourseOutlineSchema>;
export type CreateQuizRequest = z.infer<typeof createQuizSchema>;
export type UpdateQuizRequest = z.infer<typeof updateQuizSchema>;
export type PublishStageRequest = z.infer<typeof publishStageSchema>;
export type ReorderItemsRequest = z.infer<typeof reorderItemsSchema>;
