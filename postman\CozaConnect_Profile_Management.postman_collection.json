{"info": {"name": "CozaConnect Profile Management", "description": "API collection for user profile management features", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{userToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "userToken", "value": "", "type": "string"}], "item": [{"name": "Profile Management", "item": [{"name": "Get Profile (Basic)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/profile", "host": ["{{baseUrl}}"], "path": ["profile"]}}, "response": []}, {"name": "Get Profile (With Progress)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/profile?includeProgress=true", "host": ["{{baseUrl}}"], "path": ["profile"], "query": [{"key": "includeProgress", "value": "true"}]}}, "response": []}, {"name": "Get Profile (With Badges)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/profile?includeBadges=true", "host": ["{{baseUrl}}"], "path": ["profile"], "query": [{"key": "includeBadges", "value": "true"}]}}, "response": []}, {"name": "Get Profile (With Streak)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/profile?includeStreak=true", "host": ["{{baseUrl}}"], "path": ["profile"], "query": [{"key": "includeStreak", "value": "true"}]}}, "response": []}, {"name": "Get Profile (Complete)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/profile?includeProgress=true&includeBadges=true&includeStreak=true", "host": ["{{baseUrl}}"], "path": ["profile"], "query": [{"key": "includeProgress", "value": "true"}, {"key": "includeBadges", "value": "true"}, {"key": "includeStreak", "value": "true"}]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON><PERSON>\",\n  \"lastName\": \"<PERSON>gedengbe\",\n  \"gender\": \"Male\",\n  \"church\": \"Wuse Zone 5\"\n}"}, "url": {"raw": "{{baseUrl}}/profile", "host": ["{{baseUrl}}"], "path": ["profile"]}}, "response": []}]}, {"name": "PIN Management", "item": [{"name": "Change PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPin\": \"1234\",\n  \"newPin\": \"5678\",\n  \"confirmPin\": \"5678\"\n}"}, "url": {"raw": "{{baseUrl}}/profile/change-pin", "host": ["{{baseUrl}}"], "path": ["profile", "change-pin"]}}, "response": []}]}, {"name": "Profile Image Management", "item": [{"name": "Upload Profile Image", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "profileImage", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/profile/upload-image", "host": ["{{baseUrl}}"], "path": ["profile", "upload-image"]}}, "response": []}, {"name": "Delete Profile Image", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/profile/image", "host": ["{{baseUrl}}"], "path": ["profile", "image"]}}, "response": []}]}, {"name": "Authentication (For Testing)", "item": [{"name": "Send OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+2348012345678\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/send-otp", "host": ["{{baseUrl}}"], "path": ["auth", "send-otp"]}}, "response": []}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+2348012345678\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/verify-otp", "host": ["{{baseUrl}}"], "path": ["auth", "verify-otp"]}}, "response": []}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+2348012345678\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+2348012345678\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": []}]}]}