import { Response } from 'express';
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    errors?: Record<string, string[]>;
}
export declare const sendSuccess: <T>(res: Response, message: string, data?: T, statusCode?: number) => Response;
export declare const sendError: (res: Response, message: string, statusCode?: number, error?: string) => Response;
export declare const sendValidationError: (res: Response, message: string | undefined, errors: Record<string, string[]>, statusCode?: number) => Response;
export declare const sendServerError: (res: Response, message?: string, error?: string) => Response;
//# sourceMappingURL=response.utils.d.ts.map