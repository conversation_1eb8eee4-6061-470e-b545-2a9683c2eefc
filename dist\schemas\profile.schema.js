"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfileSchema = exports.changePinSchema = exports.updateProfileSchema = void 0;
const zod_1 = require("zod");
exports.updateProfileSchema = zod_1.z.object({
    firstName: zod_1.z.string().min(1, 'First name is required').max(50, 'First name too long').optional(),
    lastName: zod_1.z.string().min(1, 'Last name is required').max(50, 'Last name too long').optional(),
    gender: zod_1.z.enum(['Male', 'Female', 'Other']).optional(),
    church: zod_1.z.string().max(100, 'Church name too long').optional(),
    profileImage: zod_1.z.string().url('Invalid image URL').optional(),
});
exports.changePinSchema = zod_1.z.object({
    currentPin: zod_1.z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
    newPin: zod_1.z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
    confirmPin: zod_1.z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
}).refine((data) => data.newPin === data.confirmPin, {
    message: "New PIN and confirm PIN don't match",
    path: ["confirmPin"],
});
exports.getProfileSchema = zod_1.z.object({
    includeProgress: zod_1.z.boolean().optional(),
    includeBadges: zod_1.z.boolean().optional(),
    includeStreak: zod_1.z.boolean().optional(),
});
//# sourceMappingURL=profile.schema.js.map