"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStreakSchema = exports.updateStreakSchema = void 0;
const zod_1 = require("zod");
exports.updateStreakSchema = zod_1.z.object({
    date: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
});
exports.getStreakSchema = zod_1.z.object({
    userId: zod_1.z.string().optional(),
    startDate: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format').optional(),
    endDate: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
});
//# sourceMappingURL=streak.schema.js.map