const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixPreacherNulls() {
  try {
    console.log('Checking for stages with null preacher values...');
    
    // First, let's see how many stages have null preacher values
    const stagesWithNullPreacher = await prisma.stage.findMany({
      where: {
        preacher: null
      },
      select: {
        id: true,
        title: true,
        preacher: true
      }
    });

    console.log(`Found ${stagesWithNullPreacher.length} stages with null preacher values:`);
    stagesWithNullPreacher.forEach(stage => {
      console.log(`- ${stage.title} (ID: ${stage.id})`);
    });

    if (stagesWithNullPreacher.length > 0) {
      console.log('\nUpdating null preacher values to "Unknown Preacher"...');
      
      const updateResult = await prisma.stage.updateMany({
        where: {
          preacher: null
        },
        data: {
          preacher: 'Unknown Preacher'
        }
      });

      console.log(`Successfully updated ${updateResult.count} stages.`);
    } else {
      console.log('No stages with null preacher values found.');
    }

    console.log('\nVerifying update...');
    const remainingNulls = await prisma.stage.count({
      where: {
        preacher: null
      }
    });

    if (remainingNulls === 0) {
      console.log('✅ All stages now have preacher values!');
    } else {
      console.log(`❌ Still ${remainingNulls} stages with null preacher values.`);
    }

  } catch (error) {
    console.error('Error fixing preacher nulls:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixPreacherNulls();
