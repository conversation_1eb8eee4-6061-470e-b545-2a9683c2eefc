"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_routes_1 = __importDefault(require("./auth.routes"));
const stage_routes_1 = __importDefault(require("./stage.routes"));
const admin_routes_1 = __importDefault(require("./admin.routes"));
const badge_routes_1 = __importDefault(require("./badge.routes"));
const streak_routes_1 = __importDefault(require("./streak.routes"));
const lesson_routes_1 = __importDefault(require("./lesson.routes"));
const profile_routes_1 = __importDefault(require("./profile.routes"));
const router = (0, express_1.Router)();
router.use('/auth', auth_routes_1.default);
router.use('/stages', stage_routes_1.default);
router.use('/admin', admin_routes_1.default);
router.use('/badges', badge_routes_1.default);
router.use('/streaks', streak_routes_1.default);
router.use('/lessons', lesson_routes_1.default);
router.use('/profile', profile_routes_1.default);
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'CozaConnect API is running',
        timestamp: new Date().toISOString(),
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map