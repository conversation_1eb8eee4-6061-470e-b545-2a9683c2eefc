"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const swagger_config_1 = require("./config/swagger.config");
const routes_1 = __importDefault(require("./routes"));
const response_utils_1 = require("./utils/response.utils");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 8000;
app.use((0, cors_1.default)({
    origin: [
        "https://shopsportscontinent.com",
        "https://www.shopsportscontinent.com",
        "*",
    ],
    credentials: true,
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((req, _res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});
app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swagger_config_1.swaggerSpec, {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'CozaConnect API Documentation',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
        persistAuthorization: true,
    },
}));
app.get('/api-docs.json', (_req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swagger_config_1.swaggerSpec);
});
app.use('/api/v1', routes_1.default);
app.get('/', (_req, res) => {
    res.json({
        success: true,
        message: 'Welcome to CozaConnect API',
        version: '1.0.0',
        documentation: {
            swagger: '/api-docs',
            json: '/api-docs.json',
        },
        endpoints: {
            health: '/api/v1/health',
            auth: '/api/v1/auth',
            stages: '/api/v1/stages',
            admin: '/api/v1/admin',
            badges: '/api/v1/badges',
            streaks: '/api/v1/streaks',
            lessons: '/api/v1/lessons',
            profile: '/api/v1/profile',
        },
    });
});
app.use('*', (req, res) => {
    (0, response_utils_1.sendError)(res, `Route ${req.originalUrl} not found`, 404);
});
app.use((error, _req, res, next) => {
    console.error('Global error handler:', error);
    if (res.headersSent) {
        return next(error);
    }
    (0, response_utils_1.sendError)(res, 'Internal server error', 500, process.env.NODE_ENV === 'development' ? error.message : undefined);
});
app.listen(PORT, () => {
    console.log(`🚀 CozaConnect API server is running on http://localhost:${PORT}`);
    console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
    console.log(`📋 Swagger JSON: http://localhost:${PORT}/api-docs.json`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/api/v1/health`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
//# sourceMappingURL=index.js.map