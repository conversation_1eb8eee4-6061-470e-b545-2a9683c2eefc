"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reorderItemsSchema = exports.publishStageSchema = exports.updateQuizSchema = exports.createQuizSchema = exports.updateCourseOutlineSchema = exports.createCourseOutlineSchema = exports.updateStageSchema = exports.createStageSchema = exports.quizSchema = exports.courseOutlineSchema = exports.completeStageSchema = exports.quizAnswerSchema = void 0;
const zod_1 = require("zod");
exports.quizAnswerSchema = zod_1.z.object({
    questionId: zod_1.z.string().min(1, 'Question ID is required'),
    selectedAnswers: zod_1.z.array(zod_1.z.number().int().min(0), {
        required_error: 'Selected answers are required',
    }).min(1, 'At least one answer must be selected'),
});
exports.completeStageSchema = zod_1.z.object({
    stageId: zod_1.z.string().min(1, 'Stage ID is required'),
    answers: zod_1.z.array(exports.quizAnswerSchema).min(1, 'Quiz answers are required'),
});
exports.courseOutlineSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Outline title is required'),
    description: zod_1.z.string().optional(),
    videoUrl: zod_1.z.string().url('Invalid video URL').optional(),
    videoFile: zod_1.z.string().optional(),
    order: zod_1.z.number().int().min(1, 'Order must be positive'),
});
exports.quizSchema = zod_1.z.object({
    question: zod_1.z.string().min(1, 'Question is required'),
    questionType: zod_1.z.enum(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SINGLE_CHOICE']).default('MULTIPLE_CHOICE'),
    options: zod_1.z.array(zod_1.z.string().min(1, 'Option cannot be empty')).min(2, 'At least 2 options required'),
    correctAnswers: zod_1.z.array(zod_1.z.number().int().min(0)).min(1, 'At least one correct answer required'),
    points: zod_1.z.number().int().min(1, 'Points must be positive').default(500),
    explanation: zod_1.z.string().optional(),
    order: zod_1.z.number().int().min(1, 'Question order must be positive'),
});
exports.createStageSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Stage title is required').max(200, 'Title too long'),
    description: zod_1.z.string().optional(),
    preacher: zod_1.z.string().min(1, 'Preacher name is required').optional(),
    order: zod_1.z.number().int().min(1, 'Order must be a positive integer'),
    courseOutlines: zod_1.z.array(exports.courseOutlineSchema).min(1, 'At least one course outline is required'),
    quizzes: zod_1.z.array(exports.quizSchema).min(1, 'At least one quiz question is required'),
});
exports.updateStageSchema = exports.createStageSchema.partial();
exports.createCourseOutlineSchema = exports.courseOutlineSchema;
exports.updateCourseOutlineSchema = exports.courseOutlineSchema.partial();
exports.createQuizSchema = exports.quizSchema;
exports.updateQuizSchema = exports.quizSchema.partial();
exports.publishStageSchema = zod_1.z.object({
    isActive: zod_1.z.boolean(),
});
exports.reorderItemsSchema = zod_1.z.object({
    items: zod_1.z.array(zod_1.z.object({
        id: zod_1.z.string().min(1, 'Item ID is required'),
        order: zod_1.z.number().int().min(1, 'Order must be positive'),
    })).min(1, 'At least one item is required'),
});
//# sourceMappingURL=stage.schema.js.map