{"info": {"name": "COZA Connect - Complete API Collection", "description": "Complete API collection for COZA Connect PWA including authentication, stages, admin, and all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "stage_id", "value": "", "type": "string"}, {"key": "outline_id", "value": "", "type": "string"}, {"key": "quiz_id", "value": "", "type": "string"}, {"key": "user_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "phone_number", "value": "", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}]}, {"name": "User Authentication", "item": [{"name": "Send OTP", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('OTP sent successfully');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"09067509782\"\n}"}, "url": {"raw": "{{base_url}}/auth/send-otp", "host": ["{{base_url}}"], "path": ["auth", "send-otp"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"09067509782\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/auth/verify-otp", "host": ["{{base_url}}"], "path": ["auth", "verify-otp"]}}}, {"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('user_token', response.data.token);", "    pm.collectionVariables.set('user_id', response.data.user.id);", "    console.log('User token saved:', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"09067509782\",\n  \"fullName\": \"Test User\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('user_token', response.data.token);", "    pm.collectionVariables.set('user_id', response.data.user.id);", "    console.log('User token saved:', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"09067509782\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}]}, {"name": "User Stages & Learning", "item": [{"name": "Get Available Stages", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/stages", "host": ["{{base_url}}"], "path": ["stages"]}}}, {"name": "Get Stage Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/stages/{{stage_id}}", "host": ["{{base_url}}"], "path": ["stages", "{{stage_id}}"]}}}, {"name": "Complete Stage (Submit Quiz)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"stageId\": \"{{stage_id}}\",\n  \"answers\": [\n    {\n      \"questionId\": \"{{quiz_id}}\",\n      \"selectedAnswer\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/stages/complete", "host": ["{{base_url}}"], "path": ["stages", "complete"]}}}, {"name": "Get User Progress", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/stages/progress", "host": ["{{base_url}}"], "path": ["stages", "progress"]}}}]}, {"name": "Admin Au<PERSON>ntication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('admin_token', response.data.token);", "    console.log('Admin token saved:', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/admin/login", "host": ["{{base_url}}"], "path": ["admin", "login"]}}}]}, {"name": "Stage Management", "item": [{"name": "Get All Stages (Enhanced)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.length > 0) {", "        pm.collectionVariables.set('stage_id', response.data[0].id);", "        console.log('Stage ID saved:', response.data[0].id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/stages", "host": ["{{base_url}}"], "path": ["admin", "stages"]}}}, {"name": "Get Stage Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}"]}}}, {"name": "Create New Stage", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('stage_id', response.data.id);", "    console.log('New stage ID saved:', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Stage\",\n  \"description\": \"A test stage for API testing\",\n  \"preacher\": \"Rev. Test Preacher\",\n  \"order\": 99,\n  \"courseOutlines\": [\n    {\n      \"title\": \"Introduction\",\n      \"description\": \"Introduction to the topic\",\n      \"videoUrl\": \"https://youtube.com/watch?v=test\",\n      \"order\": 1\n    }\n  ],\n  \"quizzes\": [\n    {\n      \"question\": \"What is the main topic?\",\n      \"questionType\": \"SINGLE_CHOICE\",\n      \"options\": [\"Option A\", \"Option B\", \"Option C\"],\n      \"correctAnswer\": 1,\n      \"points\": 500,\n      \"explanation\": \"This is the correct answer\",\n      \"order\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/admin/stages", "host": ["{{base_url}}"], "path": ["admin", "stages"]}}}, {"name": "Update Stage", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Stage Title\",\n  \"description\": \"Updated description\",\n  \"preacher\": \"Rev. Updated Preacher\"\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}"]}}}, {"name": "Publish Stage", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/publish", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "publish"]}}}, {"name": "Unpublish Stage", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": false\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/publish", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "publish"]}}}, {"name": "Delete Stage", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}"]}}}]}, {"name": "Course Outline Management", "item": [{"name": "Add Course Outline", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('outline_id', response.data.id);", "    console.log('Outline ID saved:', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Course Outline\",\n  \"description\": \"Description of the course outline\",\n  \"videoUrl\": \"https://youtube.com/watch?v=newvideo\",\n  \"order\": 2\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/outlines", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "outlines"]}}}, {"name": "Update Course Outline", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Course Outline\",\n  \"description\": \"Updated description\",\n  \"videoUrl\": \"https://youtube.com/watch?v=updated\",\n  \"order\": 1\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/outlines/{{outline_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "outlines", "{{outline_id}}"]}}}, {"name": "Reorder Course Outlines", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"id\": \"{{outline_id}}\",\n      \"order\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/outlines/reorder", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "outlines", "reorder"]}}}, {"name": "Delete Course Outline", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/outlines/{{outline_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "outlines", "{{outline_id}}"]}}}]}, {"name": "Quiz Management", "item": [{"name": "Add Quiz Question", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('quiz_id', response.data.id);", "    console.log('Quiz ID saved:', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"question\": \"What does it mean to depend on <PERSON>?\",\n  \"questionType\": \"SINGLE_CHOICE\",\n  \"options\": [\n    \"Getting your opinion from friends\",\n    \"Trusting in <PERSON> with your whole heart\",\n    \"Relying on your own understanding\",\n    \"Following popular trends\"\n  ],\n  \"correctAnswer\": 1,\n  \"points\": 500,\n  \"explanation\": \"Trusting in <PERSON> with your whole heart means complete dependence on Him.\",\n  \"order\": 2\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/quizzes", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "quizzes"]}}}, {"name": "Update Quiz Question", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"question\": \"Updated: What does it mean to depend on <PERSON>?\",\n  \"questionType\": \"MULTIPLE_CHOICE\",\n  \"options\": [\n    \"Getting advice from friends\",\n    \"Trusting in <PERSON> completely\",\n    \"Relying on yourself\",\n    \"Following others\"\n  ],\n  \"correctAnswers\": [1],\n  \"points\": 600,\n  \"explanation\": \"Updated explanation: Complete trust in God is true dependence.\",\n  \"order\": 1\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/quizzes/{{quiz_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "quizzes", "{{quiz_id}}"]}}}, {"name": "Reorder Quiz Questions", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"id\": \"{{quiz_id}}\",\n      \"order\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/quizzes/reorder", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "quizzes", "reorder"]}}}, {"name": "Delete Quiz Question", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/admin/stages/{{stage_id}}/quizzes/{{quiz_id}}", "host": ["{{base_url}}"], "path": ["admin", "stages", "{{stage_id}}", "quizzes", "{{quiz_id}}"]}}}]}, {"name": "Admin Dashboard", "item": [{"name": "Get Dashboard Overview (Updated with Diamonds)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard", "host": ["{{base_url}}"], "path": ["admin", "dashboard"]}}}, {"name": "Create Admin (Super Admin Only)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newadmin\",\n  \"password\": \"newadmin123\",\n  \"fullName\": \"New Admin\",\n  \"role\": \"ADMIN\"\n}"}, "url": {"raw": "{{base_url}}/admin/create", "host": ["{{base_url}}"], "path": ["admin", "create"]}}}]}, {"name": "User Management", "item": [{"name": "Get All Users (Updated with Diamonds)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.length > 0) {", "        pm.collectionVariables.set('user_id', response.data[0].id);", "        pm.collectionVariables.set('phone_number', response.data[0].phone);", "        console.log('User ID saved:', response.data[0].id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users", "host": ["{{base_url}}"], "path": ["admin", "users"]}}}, {"name": "Get User Details (Updated with Diamonds)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}"]}}}, {"name": "Assign <PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"buddyId\": \"another_user_id\"\n}"}, "url": {"raw": "{{base_url}}/admin/users/assign-buddy", "host": ["{{base_url}}"], "path": ["admin", "users", "assign-buddy"]}}}]}, {"name": "Gift Redemption Management", "item": [{"name": "Search User by Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/admin/gifts/search-user", "host": ["{{base_url}}"], "path": ["admin", "gifts", "search-user"]}}}, {"name": "Initiate Gift Redemption (Diamond System)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"pointsToRedeem\": 10000,\n  \"pin\": \"1234\",\n  \"_comment\": \"When points are redeemed, diamonds are automatically recalculated and excess diamonds are revoked if remaining points can't support them\"\n}"}, "url": {"raw": "{{base_url}}/admin/gifts/initiate", "host": ["{{base_url}}"], "path": ["admin", "gifts", "initiate"]}}}, {"name": "Verify Gift Redemption (Legacy)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userCode\": \"USER123\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/admin/gifts/verify", "host": ["{{base_url}}"], "path": ["admin", "gifts", "verify"]}}}, {"name": "Process Gift Redemption (Legacy)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userCode\": \"USER123\",\n  \"giftType\": \"5000 Points Gift\",\n  \"pointsToDeduct\": 5000\n}"}, "url": {"raw": "{{base_url}}/admin/gifts/redeem", "host": ["{{base_url}}"], "path": ["admin", "gifts", "redeem"]}}}]}, {"name": "Reports Management", "item": [{"name": "Get All Reports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/reports", "host": ["{{base_url}}"], "path": ["admin", "reports"]}}}, {"name": "Create Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"title\": \"Weekly Progress Report\",\n  \"description\": \"User progress for this week\",\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{base_url}}/admin/reports", "host": ["{{base_url}}"], "path": ["admin", "reports"]}}}]}, {"name": "File Upload", "item": [{"name": "Upload File to Cloudinary", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/admin/upload", "host": ["{{base_url}}"], "path": ["admin", "upload"]}}}]}, {"name": "Statistics & Analytics", "item": [{"name": "Get Course Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/course-statistics", "host": ["{{base_url}}"], "path": ["admin", "course-statistics"]}}}, {"name": "Get Gift Value Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/config/gift-value", "host": ["{{base_url}}"], "path": ["admin", "config", "gift-value"]}}}, {"name": "Update Gift Value Configuration", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"giftValuePerDiamond\": 3000\n}"}, "url": {"raw": "{{base_url}}/admin/config/gift-value", "host": ["{{base_url}}"], "path": ["admin", "config", "gift-value"]}}}]}, {"name": "User Profile & Diamonds", "item": [{"name": "Get User Diamonds Summary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/profile/diamonds", "host": ["{{base_url}}"], "path": ["profile", "diamonds"]}}}]}, {"name": "Lesson Navigation", "item": [{"name": "Get Lesson by ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/stages/{{stage_id}}/lessons/{{lesson_id}}", "host": ["{{base_url}}"], "path": ["stages", "{{stage_id}}", "lessons", "{{lesson_id}}"]}}}, {"name": "Get Next Lesson", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/stages/{{stage_id}}/lessons/{{lesson_id}}/next", "host": ["{{base_url}}"], "path": ["stages", "{{stage_id}}", "lessons", "{{lesson_id}}", "next"]}}}, {"name": "Get <PERSON>on", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/stages/{{stage_id}}/lessons/{{lesson_id}}/previous", "host": ["{{base_url}}"], "path": ["stages", "{{stage_id}}", "lessons", "{{lesson_id}}", "previous"]}}}]}]}