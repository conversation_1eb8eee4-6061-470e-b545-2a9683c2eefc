"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_controller_1 = require("../controllers/auth.controller");
const validation_middleware_1 = require("../middleware/validation.middleware");
const auth_middleware_1 = require("../middleware/auth.middleware");
const auth_schema_1 = require("../schemas/auth.schema");
const router = (0, express_1.Router)();
router.post('/send-otp', (0, validation_middleware_1.validateBody)(auth_schema_1.sendOtpSchema), auth_controller_1.sendOtp);
router.post('/verify-otp', (0, validation_middleware_1.validateBody)(auth_schema_1.verifyOtpSchema), auth_controller_1.verifyOtp);
router.post('/register', (0, validation_middleware_1.validateBody)(auth_schema_1.registerSchema), auth_controller_1.register);
router.post('/login', (0, validation_middleware_1.validateBody)(auth_schema_1.loginSchema), auth_controller_1.login);
router.post('/forgot-pin', (0, validation_middleware_1.validateBody)(auth_schema_1.forgotPinSchema), auth_controller_1.forgotPin);
router.get('/profile', auth_middleware_1.authenticateUser, auth_controller_1.getProfile);
exports.default = router;
//# sourceMappingURL=auth.routes.js.map