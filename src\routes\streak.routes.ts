import { Router } from 'express';
import {
  getUserStreak,
  manualUpdateStreak,
  getStreakLeaderboard,
} from '../controllers/streak.controller';
import { authenticateUser, authenticateAdmin } from '../middleware/auth.middleware';

const router = Router();

/**
 * @swagger
 * /streaks/my-streak:
 *   get:
 *     summary: Get user's streak information
 *     description: Retrieve current streak, longest streak, and 7-day activity calendar
 *     tags: [Streaks]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User streak retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/UserStreak'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/my-streak', authenticateUser, getUserStreak);

/**
 * @swagger
 * /streaks/update:
 *   post:
 *     summary: Manual streak update
 *     description: Manually update user's streak for a specific date (for testing or admin use)
 *     tags: [Streaks]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date to mark as active (YYYY-MM-DD format, defaults to today)
 *                 example: '2024-01-15'
 *     responses:
 *       200:
 *         description: Streak updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         currentStreak:
 *                           type: integer
 *                           description: Updated current streak
 *                         longestStreak:
 *                           type: integer
 *                           description: Updated longest streak
 *                         lastActiveDate:
 *                           type: string
 *                           format: date-time
 *                           description: Last active date
 *       400:
 *         description: Invalid date format
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/update', authenticateUser, manualUpdateStreak);

/**
 * @swagger
 * /streaks/leaderboard:
 *   get:
 *     summary: Get streak leaderboard (Admin)
 *     description: Retrieve top 50 users by current streak for admin dashboard
 *     tags: [Streaks]
 *     security:
 *       - adminAuth: []
 *     responses:
 *       200:
 *         description: Streak leaderboard retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/StreakLeaderboard'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/leaderboard', authenticateAdmin, getStreakLeaderboard);

export default router;
