"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadFile = exports.getCourseStatistics = exports.initiateGiftRedemption = exports.searchUserByPhone = exports.getReports = exports.createReport = exports.reorderQuizzes = exports.reorderCourseOutlines = exports.deleteQuiz = exports.updateQuiz = exports.addQuiz = exports.deleteCourseOutline = exports.updateCourseOutline = exports.addCourseOutline = exports.publishStage = exports.getStageDetails = exports.deleteStage = exports.updateStage = exports.getAdminStages = exports.createStage = exports.assignBuddy = exports.getUserDetails = exports.getDashboardOverview = exports.getUsers = exports.createAdmin = exports.adminLogin = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const auth_utils_1 = require("../utils/auth.utils");
const auth_middleware_1 = require("../middleware/auth.middleware");
const adminLogin = async (req, res) => {
    try {
        const { username, password } = req.body;
        const admin = await db_1.default.admin.findUnique({
            where: { username },
            select: {
                id: true,
                username: true,
                password: true,
                fullName: true,
                role: true,
                isActive: true,
            },
        });
        if (!admin || !admin.isActive) {
            return (0, response_utils_1.sendError)(res, 'Invalid username or password', 401);
        }
        const isPasswordValid = await (0, auth_utils_1.comparePassword)(password, admin.password);
        if (!isPasswordValid) {
            return (0, response_utils_1.sendError)(res, 'Invalid username or password', 401);
        }
        const token = (0, auth_middleware_1.generateAdminToken)({
            id: admin.id,
            username: admin.username,
            fullName: admin.fullName,
            role: admin.role,
        });
        return (0, response_utils_1.sendSuccess)(res, 'Login successful', {
            admin: {
                id: admin.id,
                username: admin.username,
                fullName: admin.fullName,
                role: admin.role,
            },
            token,
        });
    }
    catch (error) {
        console.error('Admin login error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Login failed');
    }
};
exports.adminLogin = adminLogin;
const createAdmin = async (req, res) => {
    try {
        const { username, password, fullName, role } = req.body;
        const existingAdmin = await db_1.default.admin.findUnique({
            where: { username },
        });
        if (existingAdmin) {
            return (0, response_utils_1.sendError)(res, 'Username already exists', 409);
        }
        const hashedPassword = await (0, auth_utils_1.hashPassword)(password);
        const admin = await db_1.default.admin.create({
            data: {
                username,
                password: hashedPassword,
                fullName,
                role,
            },
            select: {
                id: true,
                username: true,
                fullName: true,
                role: true,
                createdAt: true,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Admin created successfully', admin, 201);
    }
    catch (error) {
        console.error('Create admin error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to create admin');
    }
};
exports.createAdmin = createAdmin;
const getUsers = async (req, res) => {
    try {
        const { page = 1, limit = 20, search } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const whereClause = { isActive: true };
        if (search) {
            whereClause.OR = [
                { fullName: { contains: search, mode: 'insensitive' } },
                { phone: { contains: search } },
            ];
        }
        const [users, totalCount] = await Promise.all([
            db_1.default.user.findMany({
                where: whereClause,
                select: {
                    id: true,
                    phone: true,
                    fullName: true,
                    points: true,
                    createdAt: true,
                    buddy: {
                        select: {
                            id: true,
                            fullName: true,
                            phone: true,
                        },
                    },
                    progress: {
                        select: {
                            stageId: true,
                            isCompleted: true,
                            score: true,
                            pointsEarned: true,
                            stage: {
                                select: {
                                    title: true,
                                    order: true,
                                },
                            },
                        },
                        orderBy: {
                            stage: {
                                order: 'asc',
                            },
                        },
                    },
                    giftRedemptions: {
                        select: {
                            id: true,
                            milestone: true,
                            isRedeemed: true,
                        },
                    },
                },
                skip,
                take: Number(limit),
                orderBy: { createdAt: 'desc' },
            }),
            db_1.default.user.count({ where: whereClause }),
        ]);
        const usersWithStats = users.map(user => {
            const completedStages = user.progress.filter(p => p.isCompleted).length;
            const totalStages = user.progress.length;
            const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;
            return {
                ...user,
                stats: {
                    completedStages,
                    totalStages,
                    progressPercentage,
                    pendingGifts: user.giftRedemptions.filter(g => !g.isRedeemed).length,
                },
            };
        });
        return (0, response_utils_1.sendSuccess)(res, 'Users retrieved successfully', {
            users: usersWithStats,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error('Get users error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get users');
    }
};
exports.getUsers = getUsers;
const getDashboardOverview = async (req, res) => {
    try {
        const { days = 7 } = req.query;
        const daysCount = Number(days);
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - daysCount);
        const totalUsers = await db_1.default.user.count({
            where: { isActive: true },
        });
        const newUsers = await db_1.default.user.count({
            where: {
                isActive: true,
                createdAt: {
                    gte: startDate,
                },
            },
        });
        const dailyStats = [];
        for (let i = daysCount - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const nextDate = new Date(date);
            nextDate.setDate(nextDate.getDate() + 1);
            const dayUsers = await db_1.default.user.count({
                where: {
                    isActive: true,
                    createdAt: {
                        gte: date,
                        lt: nextDate,
                    },
                },
            });
            dailyStats.push({
                date: date.toISOString().split('T')[0],
                users: dayUsers,
                label: `Jun ${String(date.getDate()).padStart(2, '0')}`,
            });
        }
        const recentUsers = await db_1.default.user.findMany({
            where: { isActive: true },
            select: {
                id: true,
                fullName: true,
                createdAt: true,
                progress: {
                    select: {
                        isCompleted: true,
                        completedAt: true,
                        stage: {
                            select: {
                                title: true,
                            },
                        },
                    },
                    where: {
                        isCompleted: true,
                    },
                    orderBy: {
                        completedAt: 'desc',
                    },
                    take: 1,
                },
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
        });
        const notifications = recentUsers.map(user => {
            const latestProgress = user.progress[0];
            if (latestProgress) {
                return {
                    id: user.id,
                    name: user.fullName,
                    action: `just completed ${latestProgress.stage.title}`,
                    time: latestProgress.completedAt,
                    type: 'stage_completed',
                };
            }
            else {
                return {
                    id: user.id,
                    name: user.fullName,
                    action: 'just joined COZA',
                    time: user.createdAt,
                    type: 'user_joined',
                };
            }
        });
        const totalStages = await db_1.default.stage.count({
            where: { isActive: true },
        });
        const completionStats = await db_1.default.userProgress.groupBy({
            by: ['isCompleted'],
            _count: {
                id: true,
            },
        });
        const completedCount = completionStats.find(stat => stat.isCompleted)?._count.id || 0;
        const totalAttempts = completionStats.reduce((sum, stat) => sum + stat._count.id, 0);
        return (0, response_utils_1.sendSuccess)(res, 'Dashboard overview retrieved successfully', {
            totalUsers,
            newUsers,
            totalStages,
            completionRate: totalAttempts > 0 ? Math.round((completedCount / totalAttempts) * 100) : 0,
            chartData: dailyStats,
            notifications,
            period: `${daysCount} days`,
        });
    }
    catch (error) {
        console.error('Get dashboard overview error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get dashboard overview');
    }
};
exports.getDashboardOverview = getDashboardOverview;
const getUserDetails = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await db_1.default.user.findUnique({
            where: { id },
            include: {
                buddy: {
                    select: {
                        id: true,
                        fullName: true,
                        phone: true,
                    },
                },
                assignedUsers: {
                    select: {
                        id: true,
                        fullName: true,
                        phone: true,
                        points: true,
                        progress: {
                            select: {
                                isCompleted: true,
                                stage: {
                                    select: {
                                        title: true,
                                        order: true,
                                    },
                                },
                            },
                            orderBy: {
                                stage: {
                                    order: 'asc',
                                },
                            },
                        },
                    },
                },
                progress: {
                    select: {
                        stageId: true,
                        isCompleted: true,
                        score: true,
                        pointsEarned: true,
                        attempts: true,
                        completedAt: true,
                        stage: {
                            select: {
                                id: true,
                                title: true,
                                order: true,
                            },
                        },
                    },
                    orderBy: {
                        stage: {
                            order: 'asc',
                        },
                    },
                },
                giftRedemptions: {
                    select: {
                        id: true,
                        milestone: true,
                        pointsRequired: true,
                        pointsDeducted: true,
                        isRedeemed: true,
                        redeemedAt: true,
                        createdAt: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                },
                pointsTransactions: {
                    select: {
                        type: true,
                        points: true,
                        description: true,
                        createdAt: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                    take: 10,
                },
            },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        const completedStages = user.progress.filter(p => p.isCompleted).length;
        const totalStages = user.progress.length;
        const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;
        const totalPointsEarned = user.progress.reduce((sum, p) => sum + p.pointsEarned, 0);
        const pendingGifts = user.giftRedemptions.filter(g => !g.isRedeemed).length;
        const redeemedGifts = user.giftRedemptions.filter(g => g.isRedeemed).length;
        const levelsCompleted = user.progress
            .filter(p => p.isCompleted)
            .map(p => ({
            stageId: p.stageId,
            title: p.stage.title,
            order: p.stage.order,
            score: p.score,
            pointsEarned: p.pointsEarned,
            completedAt: p.completedAt,
            courseLevel: `Stage ${p.stage.order}`,
        }));
        const buddyInfo = user.assignedUsers.length > 0 ? {
            assigned: true,
            buddy: user.assignedUsers[0],
        } : {
            assigned: false,
            buddy: null,
        };
        return (0, response_utils_1.sendSuccess)(res, 'User details retrieved successfully', {
            user: {
                id: user.id,
                phone: user.phone,
                fullName: user.fullName,
                points: user.points,
                createdAt: user.createdAt,
                location: 'Gwagwalada, Abuja',
                status: 'Assigned',
                courseLevel: completedStages > 0 ? `Stage ${completedStages}` : 'Not Started',
            },
            stats: {
                completedStages,
                totalStages,
                progressPercentage,
                totalPointsEarned,
                currentPoints: user.points,
                pendingGifts,
                redeemedGifts,
            },
            buddy: buddyInfo,
            levelsCompleted,
            giftRedemptions: user.giftRedemptions,
            recentTransactions: user.pointsTransactions,
        });
    }
    catch (error) {
        console.error('Get user details error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get user details');
    }
};
exports.getUserDetails = getUserDetails;
const assignBuddy = async (req, res) => {
    try {
        const { userId, buddyId } = req.body;
        const [user, buddy] = await Promise.all([
            db_1.default.user.findUnique({ where: { id: userId } }),
            db_1.default.user.findUnique({ where: { id: buddyId } }),
        ]);
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        if (!buddy) {
            return (0, response_utils_1.sendError)(res, 'Buddy not found', 404);
        }
        await db_1.default.user.update({
            where: { id: userId },
            data: { buddyId },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Buddy assigned successfully');
    }
    catch (error) {
        console.error('Assign buddy error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to assign buddy');
    }
};
exports.assignBuddy = assignBuddy;
const createStage = async (req, res) => {
    try {
        const { title, description, preacher, order, courseOutlines, quizzes } = req.body;
        const adminId = req.admin.id;
        const existingStage = await db_1.default.stage.findUnique({
            where: { order },
        });
        if (existingStage) {
            return (0, response_utils_1.sendError)(res, 'Stage order already exists', 409);
        }
        const stage = await db_1.default.$transaction(async (tx) => {
            const newStage = await tx.stage.create({
                data: {
                    title,
                    description,
                    preacher: preacher || 'Unknown Preacher',
                    order,
                    createdById: adminId,
                },
            });
            const outlineData = courseOutlines.map((outline, index) => ({
                stageId: newStage.id,
                title: outline.title,
                description: outline.description,
                videoUrl: outline.videoUrl,
                videoFile: outline.videoFile,
                order: outline.order || index + 1,
            }));
            await tx.courseOutline.createMany({
                data: outlineData,
            });
            const quizData = quizzes.map((quiz, index) => ({
                stageId: newStage.id,
                question: quiz.question,
                questionType: quiz.questionType || 'MULTIPLE_CHOICE',
                options: quiz.options,
                correctAnswers: quiz.correctAnswers,
                points: quiz.points || 500,
                explanation: quiz.explanation,
                order: quiz.order || index + 1,
            }));
            await tx.quiz.createMany({
                data: quizData,
            });
            return newStage;
        });
        return (0, response_utils_1.sendSuccess)(res, 'Stage created successfully', stage, 201);
    }
    catch (error) {
        console.error('Create stage error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to create stage');
    }
};
exports.createStage = createStage;
const getAdminStages = async (req, res) => {
    try {
        await db_1.default.stage.updateMany({
            where: { preacher: null },
            data: { preacher: 'Unknown Preacher' },
        });
        const stages = await db_1.default.stage.findMany({
            include: {
                courseOutlines: {
                    select: {
                        id: true,
                        title: true,
                        order: true,
                    },
                    orderBy: { order: 'asc' },
                },
                quizzes: {
                    select: {
                        id: true,
                        question: true,
                        options: true,
                        correctAnswers: true,
                        points: true,
                        explanation: true,
                        order: true,
                    },
                    orderBy: { order: 'asc' },
                },
                createdBy: {
                    select: {
                        fullName: true,
                        username: true,
                    },
                },
                _count: {
                    select: {
                        progress: {
                            where: { isCompleted: true },
                        },
                    },
                },
            },
            orderBy: { order: 'asc' },
        });
        const stageIds = stages.map(stage => stage.id);
        const quitCounts = await Promise.all(stageIds.map(async (stageId) => {
            const count = await db_1.default.userProgress.count({
                where: {
                    stageId,
                    isCompleted: false,
                },
            });
            return { stageId, count };
        }));
        const quitCountMap = quitCounts.reduce((acc, { stageId, count }) => {
            acc[stageId] = count;
            return acc;
        }, {});
        const enhancedStages = stages.map((stage) => ({
            id: stage.id,
            title: stage.title,
            description: stage.description,
            preacher: stage.preacher || 'Unknown Preacher',
            order: stage.order,
            isActive: stage.isActive,
            createdAt: stage.createdAt,
            updatedAt: stage.updatedAt,
            createdBy: stage.createdBy,
            completions: stage._count.progress,
            firstTimers: quitCountMap[stage.id] || 0,
            level: `Stage ${stage.order}`,
            coins: stage.quizzes.reduce((sum, quiz) => sum + quiz.points, 0),
            outlineCount: stage.courseOutlines.length,
            quizCount: stage.quizzes.length,
            courseOutlines: stage.courseOutlines,
            quizzes: stage.quizzes,
        }));
        return (0, response_utils_1.sendSuccess)(res, 'Stages retrieved successfully', enhancedStages);
    }
    catch (error) {
        console.error('Get admin stages error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get stages');
    }
};
exports.getAdminStages = getAdminStages;
const updateStage = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const stage = await db_1.default.stage.findUnique({
            where: { id },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        if (updateData.order && updateData.order !== stage.order) {
            const existingStage = await db_1.default.stage.findUnique({
                where: { order: updateData.order },
            });
            if (existingStage) {
                return (0, response_utils_1.sendError)(res, 'Stage order already exists', 409);
            }
        }
        const updatedStage = await db_1.default.stage.update({
            where: { id },
            data: {
                title: updateData.title,
                description: updateData.description,
                preacher: updateData.preacher,
                order: updateData.order,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Stage updated successfully', updatedStage);
    }
    catch (error) {
        console.error('Update stage error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to update stage');
    }
};
exports.updateStage = updateStage;
const deleteStage = async (req, res) => {
    try {
        const { id } = req.params;
        const stage = await db_1.default.stage.findUnique({
            where: { id },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        await db_1.default.stage.update({
            where: { id },
            data: { isActive: false },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Stage deleted successfully');
    }
    catch (error) {
        console.error('Delete stage error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to delete stage');
    }
};
exports.deleteStage = deleteStage;
const getStageDetails = async (req, res) => {
    try {
        const { id } = req.params;
        const stage = await db_1.default.stage.findUnique({
            where: { id },
            include: {
                courseOutlines: {
                    orderBy: { order: 'asc' },
                },
                quizzes: {
                    orderBy: { order: 'asc' },
                },
                createdBy: {
                    select: {
                        fullName: true,
                        username: true,
                    },
                },
                _count: {
                    select: {
                        progress: {
                            where: { isCompleted: true },
                        },
                    },
                },
            },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const quitCount = await db_1.default.userProgress.count({
            where: {
                stageId: id,
                isCompleted: false,
            },
        });
        const stageWithStats = {
            ...stage,
            completions: stage._count.progress,
            quitCount,
            totalPoints: stage.quizzes.reduce((sum, quiz) => sum + quiz.points, 0),
        };
        return (0, response_utils_1.sendSuccess)(res, 'Stage details retrieved successfully', stageWithStats);
    }
    catch (error) {
        console.error('Get stage details error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get stage details');
    }
};
exports.getStageDetails = getStageDetails;
const publishStage = async (req, res) => {
    try {
        const { id } = req.params;
        const { isActive } = req.body;
        const stage = await db_1.default.stage.findUnique({
            where: { id },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const updatedStage = await db_1.default.stage.update({
            where: { id },
            data: { isActive },
        });
        const action = isActive ? 'published' : 'unpublished';
        return (0, response_utils_1.sendSuccess)(res, `Stage ${action} successfully`, updatedStage);
    }
    catch (error) {
        console.error('Publish stage error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to publish stage');
    }
};
exports.publishStage = publishStage;
const addCourseOutline = async (req, res) => {
    try {
        const { id: stageId } = req.params;
        const outlineData = req.body;
        const stage = await db_1.default.stage.findUnique({
            where: { id: stageId },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const lastOutline = await db_1.default.courseOutline.findFirst({
            where: { stageId },
            orderBy: { order: 'desc' },
        });
        const nextOrder = lastOutline ? lastOutline.order + 1 : 1;
        const courseOutline = await db_1.default.courseOutline.create({
            data: {
                stageId,
                title: outlineData.title,
                description: outlineData.description,
                videoUrl: outlineData.videoUrl,
                videoFile: outlineData.videoFile,
                order: outlineData.order || nextOrder,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Course outline added successfully', courseOutline, 201);
    }
    catch (error) {
        console.error('Add course outline error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to add course outline');
    }
};
exports.addCourseOutline = addCourseOutline;
const updateCourseOutline = async (req, res) => {
    try {
        const { id: stageId, outlineId } = req.params;
        const updateData = req.body;
        const courseOutline = await db_1.default.courseOutline.findFirst({
            where: {
                id: outlineId,
                stageId,
            },
        });
        if (!courseOutline) {
            return (0, response_utils_1.sendError)(res, 'Course outline not found', 404);
        }
        const updatedOutline = await db_1.default.courseOutline.update({
            where: { id: outlineId },
            data: {
                title: updateData.title,
                description: updateData.description,
                videoUrl: updateData.videoUrl,
                videoFile: updateData.videoFile,
                order: updateData.order,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Course outline updated successfully', updatedOutline);
    }
    catch (error) {
        console.error('Update course outline error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to update course outline');
    }
};
exports.updateCourseOutline = updateCourseOutline;
const deleteCourseOutline = async (req, res) => {
    try {
        const { id: stageId, outlineId } = req.params;
        const courseOutline = await db_1.default.courseOutline.findFirst({
            where: {
                id: outlineId,
                stageId,
            },
        });
        if (!courseOutline) {
            return (0, response_utils_1.sendError)(res, 'Course outline not found', 404);
        }
        await db_1.default.courseOutline.delete({
            where: { id: outlineId },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Course outline deleted successfully');
    }
    catch (error) {
        console.error('Delete course outline error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to delete course outline');
    }
};
exports.deleteCourseOutline = deleteCourseOutline;
const addQuiz = async (req, res) => {
    try {
        const { id: stageId } = req.params;
        const quizData = req.body;
        const stage = await db_1.default.stage.findUnique({
            where: { id: stageId },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const lastQuiz = await db_1.default.quiz.findFirst({
            where: { stageId },
            orderBy: { order: 'desc' },
        });
        const nextOrder = lastQuiz ? lastQuiz.order + 1 : 1;
        const quiz = await db_1.default.quiz.create({
            data: {
                stageId,
                question: quizData.question,
                questionType: quizData.questionType || 'MULTIPLE_CHOICE',
                options: quizData.options,
                correctAnswers: quizData.correctAnswers,
                points: quizData.points || 500,
                explanation: quizData.explanation,
                order: quizData.order || nextOrder,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Quiz added successfully', quiz, 201);
    }
    catch (error) {
        console.error('Add quiz error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to add quiz');
    }
};
exports.addQuiz = addQuiz;
const updateQuiz = async (req, res) => {
    try {
        const { id: stageId, quizId } = req.params;
        const updateData = req.body;
        const quiz = await db_1.default.quiz.findFirst({
            where: {
                id: quizId,
                stageId,
            },
        });
        if (!quiz) {
            return (0, response_utils_1.sendError)(res, 'Quiz not found', 404);
        }
        const updatedQuiz = await db_1.default.quiz.update({
            where: { id: quizId },
            data: {
                question: updateData.question,
                questionType: updateData.questionType,
                options: updateData.options,
                correctAnswers: updateData.correctAnswers,
                points: updateData.points,
                explanation: updateData.explanation,
                order: updateData.order,
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Quiz updated successfully', updatedQuiz);
    }
    catch (error) {
        console.error('Update quiz error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to update quiz');
    }
};
exports.updateQuiz = updateQuiz;
const deleteQuiz = async (req, res) => {
    try {
        const { id: stageId, quizId } = req.params;
        const quiz = await db_1.default.quiz.findFirst({
            where: {
                id: quizId,
                stageId,
            },
        });
        if (!quiz) {
            return (0, response_utils_1.sendError)(res, 'Quiz not found', 404);
        }
        await db_1.default.quiz.delete({
            where: { id: quizId },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Quiz deleted successfully');
    }
    catch (error) {
        console.error('Delete quiz error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to delete quiz');
    }
};
exports.deleteQuiz = deleteQuiz;
const reorderCourseOutlines = async (req, res) => {
    try {
        const { id: stageId } = req.params;
        const { items } = req.body;
        const stage = await db_1.default.stage.findUnique({
            where: { id: stageId },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        await db_1.default.$transaction(items.map(item => db_1.default.courseOutline.update({
            where: {
                id: item.id,
                stageId,
            },
            data: { order: item.order },
        })));
        return (0, response_utils_1.sendSuccess)(res, 'Course outlines reordered successfully');
    }
    catch (error) {
        console.error('Reorder course outlines error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to reorder course outlines');
    }
};
exports.reorderCourseOutlines = reorderCourseOutlines;
const reorderQuizzes = async (req, res) => {
    try {
        const { id: stageId } = req.params;
        const { items } = req.body;
        const stage = await db_1.default.stage.findUnique({
            where: { id: stageId },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        await db_1.default.$transaction(items.map(item => db_1.default.quiz.update({
            where: {
                id: item.id,
                stageId,
            },
            data: { order: item.order },
        })));
        return (0, response_utils_1.sendSuccess)(res, 'Quizzes reordered successfully');
    }
    catch (error) {
        console.error('Reorder quizzes error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to reorder quizzes');
    }
};
exports.reorderQuizzes = reorderQuizzes;
const createReport = async (req, res) => {
    try {
        const { userId, title, content, status, tags } = req.body;
        const adminId = req.admin.id;
        const user = await db_1.default.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        const report = await db_1.default.report.create({
            data: {
                userId,
                adminId,
                title,
                content,
                status,
                tags,
            },
            include: {
                user: {
                    select: {
                        fullName: true,
                        phone: true,
                    },
                },
                admin: {
                    select: {
                        fullName: true,
                        username: true,
                    },
                },
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Report created successfully', report, 201);
    }
    catch (error) {
        console.error('Create report error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to create report');
    }
};
exports.createReport = createReport;
const getReports = async (req, res) => {
    try {
        const { page = 1, limit = 20, status, userId } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const whereClause = {};
        if (status) {
            whereClause.status = status;
        }
        if (userId) {
            whereClause.userId = userId;
        }
        const [reports, totalCount] = await Promise.all([
            db_1.default.report.findMany({
                where: whereClause,
                include: {
                    user: {
                        select: {
                            fullName: true,
                            phone: true,
                        },
                    },
                    admin: {
                        select: {
                            fullName: true,
                            username: true,
                        },
                    },
                },
                skip,
                take: Number(limit),
                orderBy: { createdAt: 'desc' },
            }),
            db_1.default.report.count({ where: whereClause }),
        ]);
        return (0, response_utils_1.sendSuccess)(res, 'Reports retrieved successfully', {
            reports,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error('Get reports error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get reports');
    }
};
exports.getReports = getReports;
const searchUserByPhone = async (req, res) => {
    try {
        const { phone } = req.body;
        const normalizedPhone = phone.replace(/^(\+234|234|0)/, '0');
        const user = await db_1.default.user.findFirst({
            where: {
                OR: [
                    { phone },
                    { phone: normalizedPhone },
                    { phone: phone.replace(/^0/, '+234') },
                    { phone: phone.replace(/^0/, '234') },
                ],
            },
            include: {
                progress: {
                    where: { isCompleted: true },
                    include: {
                        stage: {
                            select: {
                                title: true,
                                order: true,
                            },
                        },
                    },
                    orderBy: { completedAt: 'desc' },
                },
                giftRedemptions: {
                    where: { isRedeemed: false },
                    select: {
                        id: true,
                        milestone: true,
                        pointsRequired: true,
                        createdAt: true,
                    },
                    orderBy: { createdAt: 'desc' },
                },
            },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found with this phone number', 404);
        }
        const completedStages = user.progress.length;
        const currentStage = user.progress.length > 0 ? user.progress[0].stage.order + 1 : 1;
        const availableGifts = user.giftRedemptions.length;
        return (0, response_utils_1.sendSuccess)(res, 'User found successfully', {
            user: {
                id: user.id,
                fullName: user.fullName,
                phone: user.phone,
                points: user.points,
                completedStages,
                currentStage,
                availableGifts,
                joinedDate: user.createdAt,
            },
            pendingGifts: user.giftRedemptions,
        });
    }
    catch (error) {
        console.error('Search user by phone error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to search user');
    }
};
exports.searchUserByPhone = searchUserByPhone;
const initiateGiftRedemption = async (req, res) => {
    try {
        const { userId, pointsToRedeem, pin } = req.body;
        const adminId = req.admin.id;
        const user = await db_1.default.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                fullName: true,
                phone: true,
                pin: true,
                points: true,
            },
        });
        if (!user) {
            return (0, response_utils_1.sendError)(res, 'User not found', 404);
        }
        const bcrypt = require('bcryptjs');
        const isPinValid = await bcrypt.compare(pin, user.pin);
        if (!isPinValid) {
            return (0, response_utils_1.sendError)(res, 'Invalid PIN', 401);
        }
        if (user.points < pointsToRedeem) {
            return (0, response_utils_1.sendError)(res, 'Insufficient points for redemption', 400);
        }
        const userCode = (0, auth_utils_1.generateUserCode)();
        const redemption = await db_1.default.giftRedemption.create({
            data: {
                userId,
                adminId,
                milestone: `${pointsToRedeem} Points Redeemed - Code: ${userCode}`,
                pointsRequired: pointsToRedeem,
                pointsDeducted: 0,
            },
            include: {
                user: {
                    select: {
                        fullName: true,
                        phone: true,
                    },
                },
            },
        });
        return (0, response_utils_1.sendSuccess)(res, 'Gift redemption initiated successfully', {
            redemption: {
                id: redemption.id,
                userCode,
                pointsToRedeem,
                user: redemption.user,
                createdAt: redemption.createdAt,
            },
        });
    }
    catch (error) {
        console.error('Initiate gift redemption error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to initiate gift redemption');
    }
};
exports.initiateGiftRedemption = initiateGiftRedemption;
const getCourseStatistics = async (_req, res) => {
    try {
        const [stages, totalUsers, completedStages, totalPoints] = await Promise.all([
            db_1.default.stage.findMany({
                where: { isActive: true },
                include: {
                    _count: {
                        select: {
                            progress: {
                                where: { isCompleted: true },
                            },
                        },
                    },
                    courseOutlines: {
                        select: {
                            id: true,
                            title: true,
                        },
                    },
                    quizzes: {
                        select: {
                            id: true,
                            points: true,
                        },
                    },
                    createdBy: {
                        select: {
                            fullName: true,
                        },
                    },
                },
                orderBy: { order: 'asc' },
            }),
            db_1.default.user.count(),
            db_1.default.userProgress.count({
                where: { isCompleted: true },
            }),
            db_1.default.pointsTransaction.aggregate({
                where: { type: 'EARNED' },
                _sum: { points: true },
            }),
        ]);
        const stageStatistics = stages.map((stage) => ({
            id: stage.id,
            title: stage.title,
            preacher: stage.createdBy.fullName,
            order: stage.order,
            completions: stage._count.progress,
            outlineCount: stage.courseOutlines.length,
            quizCount: stage.quizzes.length,
            totalPoints: stage.quizzes.reduce((sum, quiz) => sum + quiz.points, 0),
            updatedAt: stage.updatedAt,
        }));
        return (0, response_utils_1.sendSuccess)(res, 'Course statistics retrieved successfully', {
            stages: stageStatistics,
            overview: {
                totalStages: stages.length,
                totalUsers,
                totalCompletions: completedStages,
                totalPointsAwarded: totalPoints._sum.points || 0,
            },
        });
    }
    catch (error) {
        console.error('Get course statistics error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to get course statistics');
    }
};
exports.getCourseStatistics = getCourseStatistics;
const uploadFile = async (req, res) => {
    try {
        if (!req.file) {
            return (0, response_utils_1.sendError)(res, 'No file uploaded', 400);
        }
        const fileUrl = req.file.path;
        const publicId = req.file.filename;
        return (0, response_utils_1.sendSuccess)(res, 'File uploaded successfully', {
            url: fileUrl,
            publicId,
            originalName: req.file.originalname,
            size: req.file.size,
            mimetype: req.file.mimetype,
        });
    }
    catch (error) {
        console.error('Upload file error:', error);
        return (0, response_utils_1.sendServerError)(res, 'Failed to upload file');
    }
};
exports.uploadFile = uploadFile;
//# sourceMappingURL=admin.controller.js.map