import { z } from 'zod';
export declare const updateStreakSchema: z.ZodObject<{
    date: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    date?: string | undefined;
}, {
    date?: string | undefined;
}>;
export declare const getStreakSchema: z.ZodObject<{
    userId: z.ZodOptional<z.ZodString>;
    startDate: z.ZodOptional<z.ZodString>;
    endDate: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    userId?: string | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
}, {
    userId?: string | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
}>;
export type UpdateStreakInput = z.infer<typeof updateStreakSchema>;
export type GetStreakInput = z.infer<typeof getStreakSchema>;
//# sourceMappingURL=streak.schema.d.ts.map