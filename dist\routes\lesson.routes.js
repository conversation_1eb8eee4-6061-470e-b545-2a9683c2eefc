"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const lesson_controller_1 = require("../controllers/lesson.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.get('/progress/:stageId', auth_middleware_1.authenticateUser, lesson_controller_1.getLessonProgress);
router.post('/progress', auth_middleware_1.authenticateUser, lesson_controller_1.updateLessonProgress);
router.post('/progress/bulk', auth_middleware_1.authenticateUser, lesson_controller_1.bulkUpdateLessonProgress);
router.get('/admin/progress', auth_middleware_1.authenticateAdmin, lesson_controller_1.getAdminLessonProgress);
exports.default = router;
//# sourceMappingURL=lesson.routes.js.map