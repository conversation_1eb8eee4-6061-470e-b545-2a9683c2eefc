{"version": 3, "file": "otp.utils.js", "sourceRoot": "", "sources": ["../../src/utils/otp.utils.ts"], "names": [], "mappings": ";;;AAAA,6CAA2C;AAG3C,IAAI,YAAY,GAAQ,IAAI,CAAC;AAK7B,SAAS,eAAe;IACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjC,YAAY,GAAG,MAAM,CACnB,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAC9B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAKD,MAAa,UAAU;IAMrB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAa,EAAE,GAAW;QAC7C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,eAAe,EAAE,CAAC;YAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAEZ,OAAO,CAAC,GAAG,CAAC,6BAA6B,GAAG,OAAO,KAAK,EAAE,CAAC,CAAC;gBAC5D,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;gBAC3E,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;gBAC1G,OAAO,CAAC,GAAG,CAAC,6BAA6B,GAAG,OAAO,KAAK,EAAE,CAAC,CAAC;gBAC5D,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;gBAClE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,GAAG,OAAO,KAAK,gBAAgB,CAAC,CAAC;YAE/D,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3C,IAAI,EAAE,0CAA0C,GAAG,6DAA6D;gBAChH,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;gBACrC,EAAE,EAAE,KAAK;aACV,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,yCAAyC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAGvD,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBAClD,OAAO,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,OAAO,KAAK,EAAE,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAMD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAa;QAC3C,MAAM,GAAG,GAAG,IAAA,wBAAW,GAAE,CAAC;QAC1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAE/C,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;IAC1B,CAAC;CACF;AA/DD,gCA+DC"}