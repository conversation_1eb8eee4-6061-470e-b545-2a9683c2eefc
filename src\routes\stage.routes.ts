import { Router } from 'express';
import {
  getStages,
  getStageById,
  completeStage,
  getUserProgress,
  getUserPoints,
  getLessonById,
  getNextLesson,
  getPreviousLesson,
} from '../controllers/stage.controller';
import { validateBody, validateParams } from '../middleware/validation.middleware';
import { authenticateUser } from '../middleware/auth.middleware';
import {
  completeStageSchema,
  lessonIdSchema,
  lessonNavSchema,
} from '../schemas/stage.schema';
import { z } from 'zod';

const router = Router();

// All stage routes require authentication
router.use(authenticateUser);

// Stage ID parameter validation
const stageIdSchema = z.object({
  id: z.string().min(1, 'Stage ID is required'),
});

/**
 * @swagger
 * /stages:
 *   get:
 *     tags: [Stages]
 *     summary: Get all learning stages
 *     description: Get all available learning stages with user progress information
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Stages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Stage'
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get all stages for user
router.get('/', getStages);

/**
 * @swagger
 * /stages/progress:
 *   get:
 *     tags: [Stages]
 *     summary: Get user's overall progress with individual stage percentages
 *     description: Get user's progress across all stages with individual progress percentages, lesson tracking, and comprehensive summaries
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Progress retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         totalStages:
 *                           type: integer
 *                           example: 5
 *                         completedStages:
 *                           type: integer
 *                           example: 2
 *                         progressPercentage:
 *                           type: integer
 *                           description: Overall completion percentage (completed stages / total stages)
 *                           example: 40
 *                         totalProgressPercentage:
 *                           type: integer
 *                           description: Average progress percentage across all stages
 *                           example: 65
 *                         stages:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                               title:
 *                                 type: string
 *                               order:
 *                                 type: integer
 *                               isCompleted:
 *                                 type: boolean
 *                               score:
 *                                 type: integer
 *                                 nullable: true
 *                               pointsEarned:
 *                                 type: integer
 *                               attempts:
 *                                 type: integer
 *                               completedAt:
 *                                 type: string
 *                                 format: date-time
 *                                 nullable: true
 *                               progressPercentage:
 *                                 type: integer
 *                                 description: Individual stage progress percentage
 *                                 example: 75
 *                               lessonProgress:
 *                                 type: object
 *                                 properties:
 *                                   totalLessons:
 *                                     type: integer
 *                                   completedLessons:
 *                                     type: integer
 *                                   lessonProgressPercentage:
 *                                     type: integer
 *                               quizProgress:
 *                                 type: object
 *                                 properties:
 *                                   isCompleted:
 *                                     type: boolean
 *                                   score:
 *                                     type: integer
 *                                     nullable: true
 *                                   quizProgressPercentage:
 *                                     type: integer
 *                         giftRedemptions:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/GiftRedemption'
 *                         pointsSummary:
 *                           $ref: '#/components/schemas/PointsSummary'
 *                         diamondsSummary:
 *                           type: object
 *                           properties:
 *                             totalDiamonds:
 *                               type: integer
 *                             availableDiamonds:
 *                               type: integer
 *                             redeemedDiamonds:
 *                               type: integer
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get user's overall progress
router.get('/progress', getUserProgress);

/**
 * @swagger
 * /stages/points:
 *   get:
 *     tags: [Stages]
 *     summary: Get user's points summary
 *     description: Get detailed points information and transaction history
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Points summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/PointsSummary'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get user's points summary
router.get('/points', getUserPoints);

/**
 * @swagger
 * /stages/{id}:
 *   get:
 *     tags: [Stages]
 *     summary: Get specific stage details
 *     description: Get detailed information about a specific stage including quiz questions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Stage ID
 *     responses:
 *       200:
 *         description: Stage details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         title:
 *                           type: string
 *                         description:
 *                           type: string
 *                         videoUrl:
 *                           type: string
 *                         order:
 *                           type: integer
 *                         quizzes:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Quiz'
 *                         userProgress:
 *                           type: object
 *                           nullable: true
 *                           properties:
 *                             isCompleted:
 *                               type: boolean
 *                             score:
 *                               type: integer
 *                               nullable: true
 *                             pointsEarned:
 *                               type: integer
 *                             attempts:
 *                               type: integer
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Stage is locked
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Stage not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get specific stage details
router.get('/:id', validateParams(stageIdSchema), getStageById);

/**
 * @swagger
 * /stages/complete:
 *   post:
 *     tags: [Stages]
 *     summary: Complete stage (submit quiz)
 *     description: Submit quiz answers for a stage and earn points
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - stageId
 *               - answers
 *             properties:
 *               stageId:
 *                 type: string
 *                 description: ID of the stage to complete
 *               answers:
 *                 type: array
 *                 description: Quiz answers
 *                 items:
 *                   type: object
 *                   required:
 *                     - questionId
 *                     - selectedAnswers
 *                   properties:
 *                     questionId:
 *                       type: string
 *                       description: Quiz question ID
 *                     selectedAnswers:
 *                       type: array
 *                       description: Array of selected answer indices
 *                       items:
 *                         type: integer
 *                       example: [1, 2]
 *             example:
 *               stageId: "stage-id-here"
 *               answers:
 *                 - questionId: "question-id-1"
 *                   selectedAnswers: [1, 2]
 *                 - questionId: "question-id-2"
 *                   selectedAnswers: [0]
 *     responses:
 *       200:
 *         description: Quiz submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         score:
 *                           type: integer
 *                           description: Quiz score percentage
 *                           example: 80
 *                         pointsEarned:
 *                           type: integer
 *                           description: Points earned from correct answers
 *                           example: 3000
 *                         isCompleted:
 *                           type: boolean
 *                           description: Whether stage is completed (≥60%)
 *                           example: true
 *                         attempts:
 *                           type: integer
 *                           description: Total attempts for this stage
 *                           example: 1
 *                         pointsSummary:
 *                           $ref: '#/components/schemas/PointsSummary'
 *                         newGiftsEarned:
 *                           type: integer
 *                           description: Number of new gifts earned from points
 *                           example: 1
 *                         badgeEarned:
 *                           type: object
 *                           nullable: true
 *                           description: Badge earned for completing this stage
 *                           properties:
 *                             id:
 *                               type: string
 *                               description: Badge ID
 *                             name:
 *                               type: string
 *                               description: Badge name
 *                               example: Foundation of faith
 *                             description:
 *                               type: string
 *                               description: Badge description
 *                             icon:
 *                               type: string
 *                               description: Badge icon
 *                               example: 🏆
 *                         message:
 *                           type: string
 *                           description: Success message with points info
 *       400:
 *         description: Validation error or missing answers
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Stage is locked
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Stage not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Complete stage (submit quiz)
router.post('/complete', validateBody(completeStageSchema), completeStage);

/**
 * @swagger
 * /stages/{stageId}/lessons/{lessonId}:
 *   get:
 *     tags: [Stages]
 *     summary: Get individual lesson by ID
 *     description: Get detailed information about a specific lesson within a stage
 *     parameters:
 *       - in: path
 *         name: stageId
 *         required: true
 *         schema:
 *           type: string
 *         description: Stage ID
 *       - in: path
 *         name: lessonId
 *         required: true
 *         schema:
 *           type: string
 *         description: Lesson ID
 *     responses:
 *       200:
 *         description: Lesson retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         title:
 *                           type: string
 *                         description:
 *                           type: string
 *                         videoUrl:
 *                           type: string
 *                         videoFile:
 *                           type: string
 *                         duration:
 *                           type: integer
 *                         order:
 *                           type: integer
 *                         stage:
 *                           type: object
 *                         progress:
 *                           type: object
 *       404:
 *         description: Lesson not found
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/:stageId/lessons/:lessonId', validateParams(lessonIdSchema), getLessonById);

/**
 * @swagger
 * /stages/{stageId}/lessons/{lessonId}/next:
 *   get:
 *     tags: [Stages]
 *     summary: Get next lesson in sequence
 *     description: Get the next lesson in the current stage or first lesson of next stage
 *     parameters:
 *       - in: path
 *         name: stageId
 *         required: true
 *         schema:
 *           type: string
 *         description: Stage ID
 *       - in: path
 *         name: lessonId
 *         required: true
 *         schema:
 *           type: string
 *         description: Current lesson ID
 *     responses:
 *       200:
 *         description: Navigation info retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         nextLesson:
 *                           type: object
 *                           nullable: true
 *                         nextStage:
 *                           type: object
 *                           nullable: true
 *       404:
 *         description: Current lesson not found
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/:stageId/lessons/:lessonId/next', validateParams(lessonNavSchema), getNextLesson);

/**
 * @swagger
 * /stages/{stageId}/lessons/{lessonId}/previous:
 *   get:
 *     tags: [Stages]
 *     summary: Get previous lesson in sequence
 *     description: Get the previous lesson in the current stage or last lesson of previous stage
 *     parameters:
 *       - in: path
 *         name: stageId
 *         required: true
 *         schema:
 *           type: string
 *         description: Stage ID
 *       - in: path
 *         name: lessonId
 *         required: true
 *         schema:
 *           type: string
 *         description: Current lesson ID
 *     responses:
 *       200:
 *         description: Navigation info retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         previousLesson:
 *                           type: object
 *                           nullable: true
 *                         previousStage:
 *                           type: object
 *                           nullable: true
 *       404:
 *         description: Current lesson not found
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/:stageId/lessons/:lessonId/previous', validateParams(lessonNavSchema), getPreviousLesson);

export default router;
