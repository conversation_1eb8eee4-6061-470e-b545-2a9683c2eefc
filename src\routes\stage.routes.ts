import { Router } from 'express';
import {
  getStages,
  getStageById,
  completeStage,
  getUserProgress,
  getUserPoints,
} from '../controllers/stage.controller';
import { validateBody, validateParams } from '../middleware/validation.middleware';
import { authenticateUser } from '../middleware/auth.middleware';
import { completeStageSchema } from '../schemas/stage.schema';
import { z } from 'zod';

const router = Router();

// All stage routes require authentication
router.use(authenticateUser);

// Stage ID parameter validation
const stageIdSchema = z.object({
  id: z.string().min(1, 'Stage ID is required'),
});

/**
 * @swagger
 * /stages:
 *   get:
 *     tags: [Stages]
 *     summary: Get all learning stages
 *     description: Get all available learning stages with user progress information
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Stages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Stage'
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get all stages for user
router.get('/', getStages);

/**
 * @swagger
 * /stages/progress:
 *   get:
 *     tags: [Stages]
 *     summary: Get user's overall progress
 *     description: Get user's progress across all stages with points summary
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Progress retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         totalStages:
 *                           type: integer
 *                           example: 5
 *                         completedStages:
 *                           type: integer
 *                           example: 2
 *                         progressPercentage:
 *                           type: integer
 *                           example: 40
 *                         stages:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Stage'
 *                         giftRedemptions:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/GiftRedemption'
 *                         pointsSummary:
 *                           $ref: '#/components/schemas/PointsSummary'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get user's overall progress
router.get('/progress', getUserProgress);

/**
 * @swagger
 * /stages/points:
 *   get:
 *     tags: [Stages]
 *     summary: Get user's points summary
 *     description: Get detailed points information and transaction history
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Points summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/PointsSummary'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get user's points summary
router.get('/points', getUserPoints);

/**
 * @swagger
 * /stages/{id}:
 *   get:
 *     tags: [Stages]
 *     summary: Get specific stage details
 *     description: Get detailed information about a specific stage including quiz questions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Stage ID
 *     responses:
 *       200:
 *         description: Stage details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         title:
 *                           type: string
 *                         description:
 *                           type: string
 *                         videoUrl:
 *                           type: string
 *                         order:
 *                           type: integer
 *                         quizzes:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Quiz'
 *                         userProgress:
 *                           type: object
 *                           nullable: true
 *                           properties:
 *                             isCompleted:
 *                               type: boolean
 *                             score:
 *                               type: integer
 *                               nullable: true
 *                             pointsEarned:
 *                               type: integer
 *                             attempts:
 *                               type: integer
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Stage is locked
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Stage not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Get specific stage details
router.get('/:id', validateParams(stageIdSchema), getStageById);

/**
 * @swagger
 * /stages/complete:
 *   post:
 *     tags: [Stages]
 *     summary: Complete stage (submit quiz)
 *     description: Submit quiz answers for a stage and earn points
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - stageId
 *               - answers
 *             properties:
 *               stageId:
 *                 type: string
 *                 description: ID of the stage to complete
 *               answers:
 *                 type: array
 *                 description: Quiz answers
 *                 items:
 *                   type: object
 *                   required:
 *                     - questionId
 *                     - selectedAnswers
 *                   properties:
 *                     questionId:
 *                       type: string
 *                       description: Quiz question ID
 *                     selectedAnswers:
 *                       type: array
 *                       description: Array of selected answer indices
 *                       items:
 *                         type: integer
 *                       example: [1, 2]
 *             example:
 *               stageId: "stage-id-here"
 *               answers:
 *                 - questionId: "question-id-1"
 *                   selectedAnswers: [1, 2]
 *                 - questionId: "question-id-2"
 *                   selectedAnswers: [0]
 *     responses:
 *       200:
 *         description: Quiz submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         score:
 *                           type: integer
 *                           description: Quiz score percentage
 *                           example: 80
 *                         pointsEarned:
 *                           type: integer
 *                           description: Points earned from correct answers
 *                           example: 3000
 *                         isCompleted:
 *                           type: boolean
 *                           description: Whether stage is completed (≥60%)
 *                           example: true
 *                         attempts:
 *                           type: integer
 *                           description: Total attempts for this stage
 *                           example: 1
 *                         pointsSummary:
 *                           $ref: '#/components/schemas/PointsSummary'
 *                         newGiftsEarned:
 *                           type: integer
 *                           description: Number of new gifts earned from points
 *                           example: 1
 *                         message:
 *                           type: string
 *                           description: Success message with points info
 *       400:
 *         description: Validation error or missing answers
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Stage is locked
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Stage not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Complete stage (submit quiz)
router.post('/complete', validateBody(completeStageSchema), completeStage);

export default router;
