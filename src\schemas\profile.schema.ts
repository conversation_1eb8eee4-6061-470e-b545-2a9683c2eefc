import { z } from 'zod';

// Update profile schema
export const updateProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long').optional(),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long').optional(),
  gender: z.enum(['Male', 'Female', 'Other']).optional(),
  church: z.string().max(100, 'Church name too long').optional(),
  profileImage: z.string().url('Invalid image URL').optional(),
});

// Change PIN schema
export const changePinSchema = z.object({
  currentPin: z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
  newPin: z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
  confirmPin: z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
}).refine((data) => data.newPin === data.confirmPin, {
  message: "New PIN and confirm PIN don't match",
  path: ["confirmPin"],
});

// Get profile schema (for query parameters)
export const getProfileSchema = z.object({
  includeProgress: z.boolean().optional(),
  includeBadges: z.boolean().optional(),
  includeStreak: z.boolean().optional(),
});

export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type ChangePinInput = z.infer<typeof changePinSchema>;
export type GetProfileInput = z.infer<typeof getProfileSchema>;
