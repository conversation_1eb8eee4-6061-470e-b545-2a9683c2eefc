/**
 * @swagger
 * /admin/gifts/search-user:
 *   post:
 *     summary: Search user by phone number for gift redemption
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone
 *             properties:
 *               phone:
 *                 type: string
 *                 pattern: '^(\+234|234|0)?[789][01]\d{8}$'
 *                 description: Nigerian phone number
 *                 example: "09067509782"
 *     responses:
 *       200:
 *         description: User found successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User found successfully"
 *                 data:
 *                   $ref: '#/components/schemas/UserSearchResult'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /admin/gifts/initiate:
 *   post:
 *     summary: Initiate gift redemption with PIN verification
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - pointsToRedeem
 *               - pin
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID
 *                 example: "60f7b3b3b3b3b3b3b3b3b3b3"
 *               pointsToRedeem:
 *                 type: integer
 *                 minimum: 1000
 *                 description: Points to redeem (minimum 1000)
 *                 example: 5000
 *               pin:
 *                 type: string
 *                 pattern: '^\d{4}$'
 *                 description: User's 4-digit PIN
 *                 example: "1234"
 *     responses:
 *       200:
 *         description: Gift redemption initiated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Gift redemption initiated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     redemption:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           description: Redemption ID
 *                         userCode:
 *                           type: string
 *                           description: Unique verification code
 *                           example: "ABC123XYZ"
 *                         pointsToRedeem:
 *                           type: integer
 *                           example: 5000
 *                         user:
 *                           type: object
 *                           properties:
 *                             fullName:
 *                               type: string
 *                               example: "Peterson Okopeterson"
 *                             phone:
 *                               type: string
 *                               example: "09067509782"
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *       400:
 *         description: Insufficient points or invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Invalid PIN or unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /admin/courses/statistics:
 *   get:
 *     summary: Get course statistics for dashboard
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Course statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Course statistics retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     stages:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: Stage ID
 *                           title:
 *                             type: string
 *                             description: Stage title
 *                             example: "Dominion by faith"
 *                           preacher:
 *                             type: string
 *                             description: Preacher name
 *                             example: "Rev. Biodun Fatoyinbo"
 *                           order:
 *                             type: integer
 *                             description: Stage order
 *                             example: 1
 *                           completions:
 *                             type: integer
 *                             description: Number of completions
 *                             example: 1287
 *                           outlineCount:
 *                             type: integer
 *                             description: Number of course outlines
 *                             example: 3
 *                           quizCount:
 *                             type: integer
 *                             description: Number of quiz questions
 *                             example: 5
 *                           totalPoints:
 *                             type: integer
 *                             description: Total points available
 *                             example: 2500
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                     overview:
 *                       type: object
 *                       properties:
 *                         totalStages:
 *                           type: integer
 *                           description: Total number of stages
 *                           example: 5
 *                         totalUsers:
 *                           type: integer
 *                           description: Total number of users
 *                           example: 1500
 *                         totalCompletions:
 *                           type: integer
 *                           description: Total stage completions
 *                           example: 3200
 *                         totalPointsAwarded:
 *                           type: integer
 *                           description: Total points awarded
 *                           example: 1250000
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /admin/upload:
 *   post:
 *     summary: Upload file to Cloudinary
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: File to upload (video, image, document)
 *               folder:
 *                 type: string
 *                 description: Cloudinary folder name
 *                 default: "coza-connect"
 *                 example: "coza-connect/videos"
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "File uploaded successfully"
 *                 data:
 *                   $ref: '#/components/schemas/FileUploadResponse'
 *       400:
 *         description: No file uploaded or invalid file
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
