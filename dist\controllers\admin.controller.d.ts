import { Request, Response } from 'express';
export declare const adminLogin: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const createAdmin: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUsers: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getDashboardOverview: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUserDetails: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const assignBuddy: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const createStage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getAdminStages: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateStage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const deleteStage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getStageDetails: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const publishStage: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const addCourseOutline: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateCourseOutline: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const deleteCourseOutline: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const addQuiz: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateQuiz: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const deleteQuiz: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const reorderCourseOutlines: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const reorderQuizzes: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const createReport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getReports: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const searchUserByPhone: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const initiateGiftRedemption: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getCourseStatistics: (_req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const uploadFile: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=admin.controller.d.ts.map