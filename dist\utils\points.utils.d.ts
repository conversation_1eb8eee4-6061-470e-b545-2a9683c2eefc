export declare const POINTS_CONFIG: {
    readonly POINTS_PER_CORRECT_ANSWER: 1000;
    readonly FIRST_GIFT_THRESHOLD: 5000;
    readonly SUBSEQUENT_GIFT_INTERVAL: 50000;
};
export declare const calculatePointsFromQuiz: (userAnswers: Array<{
    questionId: string;
    selectedAnswers: number[];
}>, correctAnswers: Array<{
    questionId: string;
    correctAnswers: number[];
}>) => number;
export declare const calculateEligibleGifts: (totalPoints: number) => number;
export declare const calculatePointsForNextGift: (totalPoints: number) => number;
export declare const awardPoints: (userId: string, points: number, description: string, stageId?: string) => Promise<{
    id: string;
    phone: string;
    fullName: string;
    firstName: string | null;
    lastName: string | null;
    gender: string | null;
    church: string | null;
    profileImage: string | null;
    pin: string;
    buddyId: string | null;
    points: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}>;
export declare const deductPoints: (userId: string, points: number, description: string, giftRedemptionId?: string) => Promise<{
    id: string;
    phone: string;
    fullName: string;
    firstName: string | null;
    lastName: string | null;
    gender: string | null;
    church: string | null;
    profileImage: string | null;
    pin: string;
    buddyId: string | null;
    points: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}>;
export declare const checkAndCreatePointGifts: (userId: string) => Promise<{
    id: string;
    userId: string;
    adminId: string;
    milestone: string;
    pointsRequired: number | null;
    pointsDeducted: number;
    isRedeemed: boolean;
    redeemedAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
}[]>;
export declare const getUserPointsSummary: (userId: string) => Promise<{
    totalPoints: number;
    eligibleGifts: number;
    pointsForNextGift: number;
    recentTransactions: {
        type: import(".prisma/client").$Enums.PointsTransactionType;
        points: number;
        createdAt: Date;
        description: string;
    }[];
}>;
//# sourceMappingURL=points.utils.d.ts.map