import { z } from 'zod';
export declare const adminLoginSchema: z.ZodObject<{
    username: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    username: string;
    password: string;
}, {
    username: string;
    password: string;
}>;
export declare const createAdminSchema: z.ZodObject<{
    username: z.ZodString;
    password: z.ZodString;
    fullName: z.ZodString;
    role: z.ZodDefault<z.ZodEnum<["SUPER_ADMIN", "ADMIN", "MODERATOR"]>>;
}, "strip", z.ZodTypeAny, {
    fullName: string;
    username: string;
    password: string;
    role: "SUPER_ADMIN" | "ADMIN" | "MODERATOR";
}, {
    fullName: string;
    username: string;
    password: string;
    role?: "SUPER_ADMIN" | "ADMIN" | "MODERATOR" | undefined;
}>;
export declare const assignBuddySchema: z.ZodObject<{
    userId: z.ZodString;
    buddyId: z.ZodString;
}, "strip", z.ZodType<PERSON>ny, {
    buddyId: string;
    userId: string;
}, {
    buddyId: string;
    userId: string;
}>;
export declare const createReportSchema: z.ZodObject<{
    userId: z.ZodString;
    title: z.ZodString;
    content: z.ZodString;
    status: z.ZodDefault<z.ZodEnum<["ACTIVE", "NEEDS_HELP", "COMPLETED", "PAUSED"]>>;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    status: "ACTIVE" | "NEEDS_HELP" | "COMPLETED" | "PAUSED";
    title: string;
    userId: string;
    content: string;
    tags: string[];
}, {
    title: string;
    userId: string;
    content: string;
    status?: "ACTIVE" | "NEEDS_HELP" | "COMPLETED" | "PAUSED" | undefined;
    tags?: string[] | undefined;
}>;
export declare const updateReportSchema: z.ZodObject<Omit<{
    userId: z.ZodOptional<z.ZodString>;
    title: z.ZodOptional<z.ZodString>;
    content: z.ZodOptional<z.ZodString>;
    status: z.ZodOptional<z.ZodDefault<z.ZodEnum<["ACTIVE", "NEEDS_HELP", "COMPLETED", "PAUSED"]>>>;
    tags: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
}, "userId">, "strip", z.ZodTypeAny, {
    status?: "ACTIVE" | "NEEDS_HELP" | "COMPLETED" | "PAUSED" | undefined;
    title?: string | undefined;
    content?: string | undefined;
    tags?: string[] | undefined;
}, {
    status?: "ACTIVE" | "NEEDS_HELP" | "COMPLETED" | "PAUSED" | undefined;
    title?: string | undefined;
    content?: string | undefined;
    tags?: string[] | undefined;
}>;
export declare const searchUserByPhoneSchema: z.ZodObject<{
    phone: z.ZodString;
}, "strip", z.ZodTypeAny, {
    phone: string;
}, {
    phone: string;
}>;
export declare const verifyGiftRedemptionSchema: z.ZodObject<{
    phone: z.ZodString;
    userCode: z.ZodString;
}, "strip", z.ZodTypeAny, {
    phone: string;
    userCode: string;
}, {
    phone: string;
    userCode: string;
}>;
export declare const initiateGiftRedemptionSchema: z.ZodObject<{
    userId: z.ZodString;
    pointsToRedeem: z.ZodNumber;
    pin: z.ZodString;
}, "strip", z.ZodTypeAny, {
    pin: string;
    userId: string;
    pointsToRedeem: number;
}, {
    pin: string;
    userId: string;
    pointsToRedeem: number;
}>;
export declare const fileUploadSchema: z.ZodObject<{
    file: z.ZodAny;
    folder: z.ZodDefault<z.ZodOptional<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    folder: string;
    file?: any;
}, {
    file?: any;
    folder?: string | undefined;
}>;
export type AdminLoginRequest = z.infer<typeof adminLoginSchema>;
export type CreateAdminRequest = z.infer<typeof createAdminSchema>;
export type AssignBuddyRequest = z.infer<typeof assignBuddySchema>;
export type CreateReportRequest = z.infer<typeof createReportSchema>;
export type UpdateReportRequest = z.infer<typeof updateReportSchema>;
export type SearchUserByPhoneRequest = z.infer<typeof searchUserByPhoneSchema>;
export type VerifyGiftRedemptionRequest = z.infer<typeof verifyGiftRedemptionSchema>;
export type InitiateGiftRedemptionRequest = z.infer<typeof initiateGiftRedemptionSchema>;
export type FileUploadRequest = z.infer<typeof fileUploadSchema>;
//# sourceMappingURL=admin.schema.d.ts.map