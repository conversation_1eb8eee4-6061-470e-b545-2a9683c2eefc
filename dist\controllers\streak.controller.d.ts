import { Request, Response } from 'express';
export declare const getUserStreak: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const updateUserStreak: (userId: string, activityDate?: string) => Promise<{
    id: string;
    userId: string;
    currentStreak: number;
    longestStreak: number;
    lastActiveDate: Date | null;
    streakData: import("@prisma/client/runtime/library").JsonValue | null;
    createdAt: Date;
    updatedAt: Date;
} | null>;
export declare const manualUpdateStreak: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getStreakLeaderboard: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=streak.controller.d.ts.map