"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBadgesSchema = exports.shareBadgeSchema = exports.updateBadgeSchema = exports.createBadgeSchema = void 0;
const zod_1 = require("zod");
exports.createBadgeSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Badge name is required').max(100, 'Name too long'),
    description: zod_1.z.string().min(1, 'Badge description is required').max(500, 'Description too long'),
    icon: zod_1.z.string().min(1, 'Badge icon is required'),
    stageId: zod_1.z.string().optional(),
});
exports.updateBadgeSchema = exports.createBadgeSchema.partial();
exports.shareBadgeSchema = zod_1.z.object({
    badgeId: zod_1.z.string().min(1, 'Badge ID is required'),
    platform: zod_1.z.enum(['facebook', 'twitter', 'instagram', 'whatsapp']).optional(),
});
exports.getBadgesSchema = zod_1.z.object({
    stageId: zod_1.z.string().optional(),
    isActive: zod_1.z.boolean().optional(),
});
//# sourceMappingURL=badge.schema.js.map