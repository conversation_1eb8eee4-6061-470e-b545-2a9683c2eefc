import { z } from 'zod';
export declare const phoneSchema: z.ZodString;
export declare const pinSchema: z.ZodString;
export declare const otpSchema: z.ZodString;
export declare const sendOtpSchema: z.ZodObject<{
    phone: z.ZodString;
}, "strip", z.ZodTypeAny, {
    phone: string;
}, {
    phone: string;
}>;
export declare const verifyOtpSchema: z.ZodObject<{
    phone: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    otp: string;
    phone: string;
}, {
    otp: string;
    phone: string;
}>;
export declare const registerSchema: z.ZodObject<{
    phone: z.ZodString;
    fullName: z.ZodString;
    pin: z.ZodString;
}, "strip", z.ZodTypeAny, {
    phone: string;
    fullName: string;
    pin: string;
}, {
    phone: string;
    fullName: string;
    pin: string;
}>;
export declare const loginSchema: z.ZodObject<{
    phone: z.ZodString;
    pin: z.ZodString;
}, "strip", z.ZodTypeAny, {
    phone: string;
    pin: string;
}, {
    phone: string;
    pin: string;
}>;
export declare const forgotPinSchema: z.ZodObject<{
    phone: z.ZodString;
    otp: z.ZodString;
    newPin: z.ZodString;
}, "strip", z.ZodTypeAny, {
    newPin: string;
    otp: string;
    phone: string;
}, {
    newPin: string;
    otp: string;
    phone: string;
}>;
export type SendOtpRequest = z.infer<typeof sendOtpSchema>;
export type VerifyOtpRequest = z.infer<typeof verifyOtpSchema>;
export type RegisterRequest = z.infer<typeof registerSchema>;
export type LoginRequest = z.infer<typeof loginSchema>;
export type ForgotPinRequest = z.infer<typeof forgotPinSchema>;
//# sourceMappingURL=auth.schema.d.ts.map