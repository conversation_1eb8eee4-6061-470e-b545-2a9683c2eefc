import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { sendError } from '../utils/response.utils';
import prisma from '../db/db';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        phone: string;
        fullName: string;
        type: 'user';
      };
      admin?: {
        id: string;
        username: string;
        fullName: string;
        role: string;
        type: 'admin';
      };
    }
  }
}

/**
 * Generate JWT token for user
 */
export const generateUserToken = (user: { id: string; phone: string; fullName: string }): string => {
  const payload = {
    id: user.id,
    phone: user.phone,
    fullName: user.fullName,
    type: 'user',
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET || 'your-secret-key', {
    expiresIn: '30d',
  });
};

/**
 * Generate JWT token for admin
 */
export const generateAdminToken = (admin: { id: string; username: string; fullName: string; role: string }): string => {
  const payload = {
    id: admin.id,
    username: admin.username,
    fullName: admin.fullName,
    role: admin.role,
    type: 'admin',
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET || 'your-secret-key', {
    expiresIn: '24h',
  });
};

/**
 * Middleware to authenticate user requests
 */
export const authenticateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendError(res, 'Access token required', 401);
    }
    
    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
      
      if (decoded.type !== 'user') {
        return sendError(res, 'Invalid token type', 401);
      }
      
      // Verify user still exists and is active
      const user = await prisma.user.findFirst({
        where: {
          id: decoded.id,
          isActive: true,
        },
        select: {
          id: true,
          phone: true,
          fullName: true,
        },
      });
      
      if (!user) {
        return sendError(res, 'User not found or inactive', 401);
      }
      
      req.user = {
        ...user,
        type: 'user',
      };
      
      next();
    } catch (jwtError) {
      return sendError(res, 'Invalid or expired token', 401);
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return sendError(res, 'Authentication failed', 500);
  }
};

/**
 * Middleware to authenticate admin requests
 */
export const authenticateAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendError(res, 'Access token required', 401);
    }
    
    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
      
      if (decoded.type !== 'admin') {
        return sendError(res, 'Invalid token type', 401);
      }
      
      // Verify admin still exists and is active
      const admin = await prisma.admin.findFirst({
        where: {
          id: decoded.id,
          isActive: true,
        },
        select: {
          id: true,
          username: true,
          fullName: true,
          role: true,
        },
      });
      
      if (!admin) {
        return sendError(res, 'Admin not found or inactive', 401);
      }
      
      req.admin = {
        ...admin,
        type: 'admin',
      };
      
      next();
    } catch (jwtError) {
      return sendError(res, 'Invalid or expired token', 401);
    }
  } catch (error) {
    console.error('Admin authentication error:', error);
    return sendError(res, 'Authentication failed', 500);
  }
};

/**
 * Middleware to check admin role permissions
 */
export const requireAdminRole = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.admin) {
      return sendError(res, 'Admin authentication required', 401);
    }
    
    if (!allowedRoles.includes(req.admin.role)) {
      return sendError(res, 'Insufficient permissions', 403);
    }
    
    next();
  };
};
