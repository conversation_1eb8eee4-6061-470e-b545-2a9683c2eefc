import { Request, Response } from 'express';
import prisma from '../db/db';
import { sendSuccess, sendError } from '../utils/response.utils';
import { updateProfileSchema, changePinSchema, getProfileSchema } from '../schemas/profile.schema';
import { hashPassword, comparePassword } from '../utils/auth.utils';
import { uploadToCloudinary, deleteFromCloudinary } from '../config/cloudinary.config';
import { getUserDiamondsSummary } from '../utils/diamond.utils';

/**
 * Get user profile with optional additional data
 */
export const getProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { includeProgress, includeBadges, includeStreak } = req.query;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Build include object based on query parameters
    const includeOptions: any = {};

    if (includeProgress === 'true') {
      includeOptions.progress = {
        include: {
          stage: {
            select: {
              id: true,
              title: true,
              order: true,
            },
          },
        },
        orderBy: {
          stage: {
            order: 'asc',
          },
        },
      };
    }

    if (includeBadges === 'true') {
      includeOptions.badges = {
        include: {
          badge: {
            include: {
              stage: {
                select: {
                  id: true,
                  title: true,
                  order: true,
                },
              },
            },
          },
        },
        orderBy: { earnedAt: 'desc' },
      };
    }

    if (includeStreak === 'true') {
      includeOptions.streaks = true;
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: includeOptions,
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Calculate additional stats
    let stats: any = {};

    if (includeProgress === 'true' && (user as any).progress) {
      const progress = (user as any).progress;
      const completedStages = progress.filter((p: any) => p.isCompleted).length;
      const totalStages = progress.length;
      const progressPercentage = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;

      stats = {
        ...stats,
        completedStages,
        totalStages,
        progressPercentage,
      };
    }

    if (includeBadges === 'true' && (user as any).badges) {
      stats = {
        ...stats,
        totalBadges: (user as any).badges.length,
      };
    }

    if (includeStreak === 'true' && (user as any).streaks && (user as any).streaks.length > 0) {
      const streak = (user as any).streaks[0];
      stats = {
        ...stats,
        currentStreak: streak.currentStreak,
        longestStreak: streak.longestStreak,
      };
    }

    // Format response
    const profile: any = {
      id: user.id,
      phone: user.phone,
      fullName: user.fullName,
      firstName: user.firstName,
      lastName: user.lastName,
      gender: user.gender,
      church: user.church,
      profileImage: user.profileImage,
      points: user.points,
      createdAt: user.createdAt,
      stats,
    };

    // Add optional data if requested
    if (includeProgress === 'true') {
      profile.progress = (user as any).progress;
    }

    if (includeBadges === 'true') {
      const badges = (user as any).badges;
      profile.badges = badges?.map((userBadge: any) => ({
        id: userBadge.badge.id,
        name: userBadge.badge.name,
        description: userBadge.badge.description,
        icon: userBadge.badge.icon,
        stage: userBadge.badge.stage,
        earnedAt: userBadge.earnedAt,
        isShared: userBadge.isShared,
      }));
    }

    if (includeStreak === 'true') {
      const streaks = (user as any).streaks;
      profile.streak = streaks?.[0] || null;
    }

    return sendSuccess(res, 'Profile retrieved successfully', profile);
  } catch (error) {
    console.error('Get profile error:', error);
    return sendError(res, 'Failed to retrieve profile');
  }
};

/**
 * Update user profile
 */
export const updateProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const validatedData = updateProfileSchema.parse(req.body);

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    const { firstName, lastName, gender, church, profileImage } = validatedData;

    // Prepare update data
    const updateData: any = {};

    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (gender !== undefined) updateData.gender = gender;
    if (church !== undefined) updateData.church = church;
    if (profileImage !== undefined) updateData.profileImage = profileImage;

    // Update fullName if firstName or lastName changed
    if (firstName !== undefined || lastName !== undefined) {
      const currentUser = await prisma.user.findUnique({
        where: { id: userId },
        select: { firstName: true, lastName: true },
      });

      const newFirstName = firstName ?? currentUser?.firstName ?? '';
      const newLastName = lastName ?? currentUser?.lastName ?? '';
      updateData.fullName = `${newFirstName} ${newLastName}`.trim();
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        phone: true,
        fullName: true,
        firstName: true,
        lastName: true,
        gender: true,
        church: true,
        profileImage: true,
        points: true,
        updatedAt: true,
      },
    });

    return sendSuccess(res, 'Profile updated successfully', updatedUser);
  } catch (error) {
    console.error('Update profile error:', error);
    return sendError(res, 'Failed to update profile');
  }
};

/**
 * Change user PIN
 */
export const changePin = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const validatedData = changePinSchema.parse(req.body);

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    const { currentPin, newPin } = validatedData;

    // Get current user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { pin: true },
    });

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Verify current PIN
    const isCurrentPinValid = await comparePassword(currentPin, user.pin);
    if (!isCurrentPinValid) {
      return sendError(res, 'Current PIN is incorrect', 400);
    }

    // Hash new PIN
    const hashedNewPin = await hashPassword(newPin);

    // Update PIN
    await prisma.user.update({
      where: { id: userId },
      data: { pin: hashedNewPin },
    });

    return sendSuccess(res, 'PIN changed successfully');
  } catch (error) {
    console.error('Change PIN error:', error);
    return sendError(res, 'Failed to change PIN');
  }
};

/**
 * Upload profile image
 */
export const uploadProfileImage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    if (!req.file) {
      return sendError(res, 'No image file uploaded', 400);
    }

    // Upload file to Cloudinary
    const uploadResult = await uploadToCloudinary(req.file, 'coza-connect/profiles');

    // Update user profile image
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { profileImage: uploadResult.url },
      select: {
        id: true,
        profileImage: true,
        updatedAt: true,
      },
    });

    return sendSuccess(res, 'Profile image uploaded successfully', {
      profileImage: updatedUser.profileImage,
      updatedAt: updatedUser.updatedAt,
    });
  } catch (error) {
    console.error('Upload profile image error:', error);
    return sendError(res, 'Failed to upload profile image');
  }
};

/**
 * Delete profile image
 */
export const deleteProfileImage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Remove profile image
    await prisma.user.update({
      where: { id: userId },
      data: { profileImage: null },
    });

    return sendSuccess(res, 'Profile image deleted successfully');
  } catch (error) {
    console.error('Delete profile image error:', error);
    return sendError(res, 'Failed to delete profile image');
  }
};

/**
 * Get user's diamonds summary
 */
export const getUserDiamonds = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    const diamondsSummary = await getUserDiamondsSummary(userId);

    return sendSuccess(res, 'Diamonds summary retrieved successfully', diamondsSummary);
  } catch (error) {
    console.error('Get user diamonds error:', error);
    return sendError(res, 'Failed to get diamonds summary');
  }
};
