# 🚀 CozaConnect API - Complete Documentation Summary

## 📋 Overview

The CozaConnect API has been enhanced with comprehensive gamification features, profile management, and detailed progress tracking. All endpoints are fully documented in Swagger and ready for frontend integration.

**🌐 API Base URL**: `http://localhost:8000/api/v1`  
**📚 Swagger Documentation**: `http://localhost:8000/api-docs`  
**🏥 Health Check**: `http://localhost:8000/api/v1/health`

---

## 🎯 New Features Added

### 🏆 Badge System
- **Automatic Badge Awarding**: Users earn badges when completing stages
- **Badge Collection**: View all earned badges with metadata
- **Social Sharing**: Share badges on social media platforms
- **Admin Management**: Create, update, and delete badges

### 🔥 Streak Tracking
- **Daily Activity**: Track consecutive days of learning activity
- **Calendar View**: 7-day activity calendar with visual indicators
- **Streak Statistics**: Current streak, longest streak, total active days
- **Leaderboard**: Admin view of top users by streak

### 📚 Lesson Progress Tracking
- **Individual Lessons**: Track completion and watch time for each lesson
- **Progress Percentages**: Calculate completion percentages
- **Bulk Updates**: Update multiple lessons at once
- **Admin Analytics**: Detailed progress reports for administrators

### 👤 Enhanced Profile Management
- **Extended User Profiles**: First name, last name, gender, church location
- **Profile Images**: Upload and manage profile pictures via Cloudinary
- **PIN Management**: Secure PIN change functionality
- **Comprehensive Stats**: Include progress, badges, and streak data

---

## 🛠 API Endpoints Summary

### 🔐 Authentication Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/auth/send-otp` | Send SMS OTP for verification |
| `POST` | `/auth/verify-otp` | Verify OTP code |
| `POST` | `/auth/register` | Register new user |
| `POST` | `/auth/login` | User login with phone/PIN |
| `GET` | `/auth/profile` | Get authenticated user profile |

### 👤 Profile Management Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/profile` | Get user profile (with optional data) |
| `PUT` | `/profile` | Update user profile |
| `POST` | `/profile/change-pin` | Change user PIN |
| `POST` | `/profile/upload-image` | Upload profile image |
| `DELETE` | `/profile/image` | Delete profile image |

**Query Parameters for GET `/profile`:**
- `includeProgress=true` - Include stage progress
- `includeBadges=true` - Include earned badges
- `includeStreak=true` - Include streak information

### 🏆 Badge System Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/badges/my-badges` | Get user's earned badges |
| `POST` | `/badges/share` | Share a badge on social media |
| `POST` | `/badges` | Create new badge (Admin) |
| `GET` | `/badges` | Get all badges with stats (Admin) |
| `PUT` | `/badges/:id` | Update badge (Admin) |
| `DELETE` | `/badges/:id` | Delete badge (Admin) |

### 🔥 Streak Tracking Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/streaks/my-streak` | Get user's streak information |
| `POST` | `/streaks/update` | Manual streak update |
| `GET` | `/streaks/leaderboard` | Get streak leaderboard (Admin) |

### 📚 Lesson Progress Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/lessons/progress/:stageId` | Get lesson progress for stage |
| `POST` | `/lessons/progress` | Update single lesson progress |
| `POST` | `/lessons/progress/bulk` | Bulk update lesson progress |
| `GET` | `/lessons/admin/progress` | Admin lesson progress view |

### 🎓 Learning Stage Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/stages` | Get all stages with user progress |
| `GET` | `/stages/:id` | Get specific stage details |
| `POST` | `/stages/complete` | Complete stage (submit quiz) |

**Enhanced Stage Completion Response:**
```json
{
  "success": true,
  "message": "Quiz submitted successfully",
  "data": {
    "score": 80,
    "pointsEarned": 3000,
    "isCompleted": true,
    "attempts": 1,
    "pointsSummary": { /* points data */ },
    "newGiftsEarned": 1,
    "badgeEarned": {
      "id": "badge_id",
      "name": "Foundation of faith",
      "description": "You did it! Each step you take draws you closer to knowing Christ more deeply.",
      "icon": "🏆"
    },
    "message": "Congratulations! You passed this stage and earned 3000 points!"
  }
}
```

### 🛡 Admin Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/admin/dashboard` | Admin dashboard overview |
| `GET` | `/admin/users` | Get all users with pagination |
| `GET` | `/admin/users/:id` | Get specific user details |
| `GET` | `/admin/stages` | Get all stages (admin view) |
| `POST` | `/admin/stages` | Create new stage |
| `PUT` | `/admin/stages/:id` | Update stage |
| `DELETE` | `/admin/stages/:id` | Delete stage |
| `POST` | `/admin/upload` | Upload files to Cloudinary |

---

## 🎮 Gamification Features

### 🏆 Badge System
- **Automatic Awarding**: Badges are automatically awarded when users complete stages
- **Collection View**: Users can view all their earned badges
- **Social Sharing**: Share achievements on social platforms
- **Stage Association**: Each badge is linked to a specific learning stage

### 🔥 Streak System
- **Daily Tracking**: Automatically tracks daily learning activity
- **Visual Calendar**: 7-day calendar showing active/inactive days
- **Statistics**: Current streak, longest streak, total active days
- **Leaderboard**: Competition element with top streaks

### 📊 Progress Tracking
- **Lesson Level**: Individual lesson completion and watch time
- **Stage Level**: Overall stage completion percentages
- **User Level**: Complete learning journey overview
- **Admin Analytics**: Detailed progress reports and statistics

---

## 🔧 Authentication & Security

### 🔐 JWT Authentication
- **Bearer Token**: Include in Authorization header
- **User Level**: `Authorization: Bearer <user-token>`
- **Admin Level**: `Authorization: Bearer <admin-token>`

### 📱 Phone Number Format
Supports Nigerian phone number formats:
- `+2348012345678` (preferred)
- `2348012345678`
- `08012345678`

### 🔒 PIN Security
- 4-digit numeric PIN for user authentication
- Secure PIN change functionality
- Hashed storage in database

---

## 📊 Data Models

### 👤 Enhanced User Model
```typescript
interface User {
  id: string;
  phone: string;
  fullName: string;
  firstName?: string;
  lastName?: string;
  gender?: 'Male' | 'Female' | 'Other';
  church?: string;
  profileImage?: string;
  points: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### 🏆 Badge Model
```typescript
interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  stage?: {
    id: string;
    title: string;
    order: number;
  };
  earnedAt: Date;
  isShared: boolean;
}
```

### 🔥 Streak Model
```typescript
interface UserStreak {
  currentStreak: number;
  longestStreak: number;
  lastActiveDate: Date;
  last7Days: Array<{
    date: string;
    day: string;
    isActive: boolean;
  }>;
  totalActiveDays: number;
}
```

### 📚 Lesson Progress Model
```typescript
interface LessonProgress {
  id: string;
  title: string;
  description: string;
  videoUrl?: string;
  videoFile?: string;
  duration?: number;
  order: number;
  progress: {
    isCompleted: boolean;
    watchTime: number;
    completedAt?: Date;
    progressPercentage: number;
  };
}
```

---

## 🚀 Frontend Integration Guide

### 1. **Authentication Flow**
```javascript
// 1. Send OTP
const otpResponse = await fetch('/api/v1/auth/send-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ phone: '+2348012345678' })
});

// 2. Verify OTP
const verifyResponse = await fetch('/api/v1/auth/verify-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ phone: '+2348012345678', otp: '123456' })
});

// 3. Register or Login
const authResponse = await fetch('/api/v1/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ 
    phone: '+2348012345678', 
    fullName: 'John Doe', 
    pin: '1234' 
  })
});

const { token } = authResponse.data;
```

### 2. **Profile Management**
```javascript
// Get complete profile with all data
const profileResponse = await fetch('/api/v1/profile?includeProgress=true&includeBadges=true&includeStreak=true', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// Update profile
const updateResponse = await fetch('/api/v1/profile', {
  method: 'PUT',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    firstName: 'John',
    lastName: 'Doe',
    gender: 'Male',
    church: 'Wuse Zone 5'
  })
});

// Upload profile image
const formData = new FormData();
formData.append('profileImage', imageFile);
const uploadResponse = await fetch('/api/v1/profile/upload-image', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: formData
});
```

### 3. **Learning Progress**
```javascript
// Get lesson progress for a stage
const progressResponse = await fetch(`/api/v1/lessons/progress/${stageId}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});

// Update lesson progress
const updateProgress = await fetch('/api/v1/lessons/progress', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    courseOutlineId: 'lesson_id',
    watchTime: 120,
    isCompleted: true
  })
});

// Complete stage (submit quiz)
const completeStage = await fetch('/api/v1/stages/complete', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    stageId: 'stage_id',
    answers: [
      { questionId: 'q1', selectedAnswers: [1, 2] },
      { questionId: 'q2', selectedAnswers: [0] }
    ]
  })
});
```

### 4. **Gamification Features**
```javascript
// Get user badges
const badgesResponse = await fetch('/api/v1/badges/my-badges', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// Get user streak
const streakResponse = await fetch('/api/v1/streaks/my-streak', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// Share badge
const shareResponse = await fetch('/api/v1/badges/share', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    badgeId: 'badge_id',
    platform: 'facebook'
  })
});
```

---

## 📋 Postman Collections

Three comprehensive Postman collections have been created:

1. **CozaConnect_Enhanced_Features.postman_collection.json**
   - Badge management endpoints
   - Streak tracking endpoints
   - Lesson progress endpoints
   - Enhanced stage completion

2. **CozaConnect_Profile_Management.postman_collection.json**
   - Profile CRUD operations
   - PIN management
   - Image upload/delete
   - Authentication examples

3. **Original collections** (existing)
   - Authentication flows
   - Stage management
   - Admin operations

---

## 🎯 Key Benefits for Frontend

### 🚀 **Enhanced User Experience**
- **Gamification**: Points, badges, and streaks increase engagement
- **Progress Tracking**: Detailed progress visualization
- **Profile Customization**: Rich user profiles with images
- **Social Features**: Badge sharing capabilities

### 📊 **Rich Data Access**
- **Comprehensive APIs**: All data accessible via well-documented endpoints
- **Flexible Queries**: Optional data inclusion for optimized requests
- **Real-time Updates**: Immediate feedback on user actions
- **Admin Analytics**: Detailed insights for administrators

### 🔧 **Developer Friendly**
- **Complete Swagger Documentation**: Interactive API explorer
- **TypeScript Types**: Full type definitions available
- **Postman Collections**: Ready-to-use API testing
- **Error Handling**: Consistent error responses

---

## 🚀 Next Steps

1. **Import Postman Collections**: Use the provided collections for API testing
2. **Review Swagger Documentation**: Explore all endpoints at `/api-docs`
3. **Implement Authentication**: Start with the auth flow
4. **Build Profile Management**: Implement user profile features
5. **Add Gamification**: Integrate badges, streaks, and progress tracking
6. **Test Thoroughly**: Use the provided examples and test cases

---

## 📞 Support

For any questions or issues with the API:
- **Swagger Documentation**: `http://localhost:8000/api-docs`
- **Health Check**: `http://localhost:8000/api/v1/health`
- **Postman Collections**: Available in the `postman/` directory

The API is now fully ready for frontend integration with comprehensive gamification features, detailed progress tracking, and enhanced user management! 🎉
