import { Router } from 'express';
import {
  createBadge,
  getAllBadges,
  getUserBadges,
  shareBadge,
  updateBadge,
  deleteBadge,
} from '../controllers/badge.controller';
import { authenticateUser, authenticateAdmin } from '../middleware/auth.middleware';

const router = Router();

/**
 * @swagger
 * /badges/my-badges:
 *   get:
 *     summary: Get user's badges
 *     description: Retrieve all badges earned by the authenticated user
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User badges retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Badge'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/my-badges', authenticateUser, getUserBadges);

/**
 * @swagger
 * /badges/share:
 *   post:
 *     summary: Share a badge
 *     description: Share an earned badge on social media platforms
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               badgeId:
 *                 type: string
 *                 description: ID of the badge to share
 *               platform:
 *                 type: string
 *                 enum: [facebook, twitter, instagram, whatsapp]
 *                 description: Social media platform (optional)
 *             required:
 *               - badgeId
 *     responses:
 *       200:
 *         description: Badge shared successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         title:
 *                           type: string
 *                           description: Share title
 *                         description:
 *                           type: string
 *                           description: Share description
 *                         url:
 *                           type: string
 *                           description: Share URL
 *                         image:
 *                           type: string
 *                           description: Badge image
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Badge not found or not earned by user
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/share', authenticateUser, shareBadge);

/**
 * @swagger
 * /badges:
 *   post:
 *     summary: Create a new badge (Admin)
 *     description: Create a new badge for stage completion
 *     tags: [Badges]
 *     security:
 *       - adminAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Badge name
 *                 example: Foundation of faith
 *               description:
 *                 type: string
 *                 description: Badge description
 *                 example: You did it! Each step you take draws you closer to knowing Christ more deeply.
 *               icon:
 *                 type: string
 *                 description: Badge icon (emoji or URL)
 *                 example: 🏆
 *               stageId:
 *                 type: string
 *                 description: Stage ID this badge is for (optional)
 *             required:
 *               - name
 *               - description
 *               - icon
 *     responses:
 *       201:
 *         description: Badge created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Badge'
 *       400:
 *         description: Badge with this name already exists or validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Stage not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   get:
 *     summary: Get all badges (Admin)
 *     description: Retrieve all badges with statistics
 *     tags: [Badges]
 *     security:
 *       - adminAuth: []
 *     responses:
 *       200:
 *         description: Badges retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/Badge'
 *                           - type: object
 *                             properties:
 *                               _count:
 *                                 type: object
 *                                 properties:
 *                                   userBadges:
 *                                     type: integer
 *                                     description: Number of users who earned this badge
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/', authenticateAdmin, createBadge);
router.get('/', authenticateAdmin, getAllBadges);

/**
 * @swagger
 * /badges/{id}:
 *   put:
 *     summary: Update a badge (Admin)
 *     description: Update badge information
 *     tags: [Badges]
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Badge ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Badge name
 *               description:
 *                 type: string
 *                 description: Badge description
 *               icon:
 *                 type: string
 *                 description: Badge icon
 *               stageId:
 *                 type: string
 *                 description: Stage ID
 *     responses:
 *       200:
 *         description: Badge updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Badge'
 *       404:
 *         description: Badge not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   delete:
 *     summary: Delete a badge (Admin)
 *     description: Delete a badge and all associated user badges
 *     tags: [Badges]
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Badge ID
 *     responses:
 *       200:
 *         description: Badge deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       404:
 *         description: Badge not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.put('/:id', authenticateAdmin, updateBadge);
router.delete('/:id', authenticateAdmin, deleteBadge);

export default router;
