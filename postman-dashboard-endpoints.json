{"info": {"name": "CozaConnect Dashboard Endpoints", "description": "New dashboard endpoints for admin panel", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Dashboard Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/dashboard", "host": ["{{base_url}}"], "path": ["admin", "dashboard"]}, "description": "Get dashboard overview with statistics, chart data, and notifications"}, "response": []}, {"name": "Dashboard Overview (14 days)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/dashboard?days=14", "host": ["{{base_url}}"], "path": ["admin", "dashboard"], "query": [{"key": "days", "value": "14", "description": "Number of days for chart data (1-30)"}]}, "description": "Get dashboard overview with 14 days of chart data"}, "response": []}, {"name": "Dashboard Overview (30 days)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/dashboard?days=30", "host": ["{{base_url}}"], "path": ["admin", "dashboard"], "query": [{"key": "days", "value": "30", "description": "Number of days for chart data (1-30)"}]}, "description": "Get dashboard overview with 30 days of chart data"}, "response": []}, {"name": "Get All Users (First Timers)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/users?page=1&limit=20", "host": ["{{base_url}}"], "path": ["admin", "users"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Number of users per page"}]}, "description": "Get paginated list of all users (First Timers list)"}, "response": []}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/users?search=peterson&limit=10", "host": ["{{base_url}}"], "path": ["admin", "users"], "query": [{"key": "search", "value": "peterson", "description": "Search by name or phone number"}, {"key": "limit", "value": "10", "description": "Number of users per page"}]}, "description": "Search users by name or phone number"}, "response": []}, {"name": "Get User Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}"]}, "description": "Get detailed information about a specific user including progress, buddy, and gift redemptions"}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "admin_token", "value": "", "type": "string", "description": "Admin JWT token - get this from admin login"}, {"key": "user_id", "value": "", "type": "string", "description": "User ID for detailed view - get this from users list"}]}