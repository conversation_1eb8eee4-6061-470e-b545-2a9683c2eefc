"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAdminLessonProgress = exports.bulkUpdateLessonProgress = exports.updateLessonProgress = exports.getLessonProgress = void 0;
const db_1 = __importDefault(require("../db/db"));
const response_utils_1 = require("../utils/response.utils");
const lesson_schema_1 = require("../schemas/lesson.schema");
const streak_controller_1 = require("./streak.controller");
const getLessonProgress = async (req, res) => {
    try {
        const { stageId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const stage = await db_1.default.stage.findUnique({
            where: { id: stageId },
            include: {
                courseOutlines: {
                    include: {
                        lessonProgress: {
                            where: { userId },
                        },
                    },
                    orderBy: { order: 'asc' },
                },
            },
        });
        if (!stage) {
            return (0, response_utils_1.sendError)(res, 'Stage not found', 404);
        }
        const lessonsWithProgress = stage.courseOutlines.map(outline => {
            const progress = outline.lessonProgress[0] || null;
            return {
                id: outline.id,
                title: outline.title,
                description: outline.description,
                videoUrl: outline.videoUrl,
                videoFile: outline.videoFile,
                duration: outline.duration,
                order: outline.order,
                progress: progress ? {
                    isCompleted: progress.isCompleted,
                    watchTime: progress.watchTime,
                    completedAt: progress.completedAt,
                    progressPercentage: outline.duration
                        ? Math.min(100, Math.round((progress.watchTime / outline.duration) * 100))
                        : 0,
                } : {
                    isCompleted: false,
                    watchTime: 0,
                    completedAt: null,
                    progressPercentage: 0,
                },
            };
        });
        const totalLessons = lessonsWithProgress.length;
        const completedLessons = lessonsWithProgress.filter(lesson => lesson.progress.isCompleted).length;
        const stageProgressPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
        const response = {
            stage: {
                id: stage.id,
                title: stage.title,
                description: stage.description,
                order: stage.order,
            },
            lessons: lessonsWithProgress,
            summary: {
                totalLessons,
                completedLessons,
                progressPercentage: stageProgressPercentage,
            },
        };
        return (0, response_utils_1.sendSuccess)(res, 'Lesson progress retrieved successfully', response);
    }
    catch (error) {
        console.error('Get lesson progress error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve lesson progress');
    }
};
exports.getLessonProgress = getLessonProgress;
const updateLessonProgress = async (req, res) => {
    try {
        const validatedData = lesson_schema_1.updateLessonProgressSchema.parse(req.body);
        const { courseOutlineId, watchTime, isCompleted } = validatedData;
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const courseOutline = await db_1.default.courseOutline.findUnique({
            where: { id: courseOutlineId },
            include: {
                stage: true,
            },
        });
        if (!courseOutline) {
            return (0, response_utils_1.sendError)(res, 'Course outline not found', 404);
        }
        let lessonProgress = await db_1.default.lessonProgress.findUnique({
            where: {
                userId_courseOutlineId: {
                    userId,
                    courseOutlineId,
                },
            },
        });
        const updateData = {};
        if (watchTime !== undefined) {
            updateData.watchTime = watchTime;
            updateData.totalDuration = courseOutline.duration;
        }
        if (isCompleted !== undefined) {
            updateData.isCompleted = isCompleted;
            if (isCompleted) {
                updateData.completedAt = new Date();
            }
        }
        if (lessonProgress) {
            lessonProgress = await db_1.default.lessonProgress.update({
                where: {
                    userId_courseOutlineId: {
                        userId,
                        courseOutlineId,
                    },
                },
                data: updateData,
            });
        }
        else {
            lessonProgress = await db_1.default.lessonProgress.create({
                data: {
                    userId,
                    courseOutlineId,
                    ...updateData,
                },
            });
        }
        if (isCompleted) {
            await (0, streak_controller_1.updateUserStreak)(userId);
        }
        const stageProgress = await checkStageCompletion(userId, courseOutline.stageId);
        return (0, response_utils_1.sendSuccess)(res, 'Lesson progress updated successfully', {
            lessonProgress,
            stageProgress,
        });
    }
    catch (error) {
        console.error('Update lesson progress error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to update lesson progress');
    }
};
exports.updateLessonProgress = updateLessonProgress;
const bulkUpdateLessonProgress = async (req, res) => {
    try {
        const validatedData = lesson_schema_1.bulkUpdateLessonProgressSchema.parse(req.body);
        const { lessons } = validatedData;
        const userId = req.user?.id;
        if (!userId) {
            return (0, response_utils_1.sendError)(res, 'User not authenticated', 401);
        }
        const updatedLessons = [];
        let hasCompletedLesson = false;
        for (const lessonUpdate of lessons) {
            const { courseOutlineId, watchTime, isCompleted } = lessonUpdate;
            const courseOutline = await db_1.default.courseOutline.findUnique({
                where: { id: courseOutlineId },
            });
            if (!courseOutline) {
                continue;
            }
            const updateData = {};
            if (watchTime !== undefined) {
                updateData.watchTime = watchTime;
                updateData.totalDuration = courseOutline.duration;
            }
            if (isCompleted !== undefined) {
                updateData.isCompleted = isCompleted;
                if (isCompleted) {
                    updateData.completedAt = new Date();
                    hasCompletedLesson = true;
                }
            }
            const lessonProgress = await db_1.default.lessonProgress.upsert({
                where: {
                    userId_courseOutlineId: {
                        userId,
                        courseOutlineId,
                    },
                },
                update: updateData,
                create: {
                    userId,
                    courseOutlineId,
                    ...updateData,
                },
            });
            updatedLessons.push(lessonProgress);
        }
        if (hasCompletedLesson) {
            await (0, streak_controller_1.updateUserStreak)(userId);
        }
        return (0, response_utils_1.sendSuccess)(res, 'Lesson progress updated successfully', {
            updatedLessons: updatedLessons.length,
            lessons: updatedLessons,
        });
    }
    catch (error) {
        console.error('Bulk update lesson progress error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to update lesson progress');
    }
};
exports.bulkUpdateLessonProgress = bulkUpdateLessonProgress;
async function checkStageCompletion(userId, stageId) {
    try {
        const courseOutlines = await db_1.default.courseOutline.findMany({
            where: { stageId },
            include: {
                lessonProgress: {
                    where: { userId },
                },
            },
        });
        const totalLessons = courseOutlines.length;
        const completedLessons = courseOutlines.filter(outline => outline.lessonProgress[0]?.isCompleted).length;
        const isStageCompleted = totalLessons > 0 && completedLessons === totalLessons;
        return {
            totalLessons,
            completedLessons,
            isCompleted: isStageCompleted,
            progressPercentage: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0,
        };
    }
    catch (error) {
        console.error('Check stage completion error:', error);
        return {
            totalLessons: 0,
            completedLessons: 0,
            isCompleted: false,
            progressPercentage: 0,
        };
    }
}
const getAdminLessonProgress = async (req, res) => {
    try {
        const { userId, stageId } = req.query;
        const whereClause = {};
        if (userId) {
            whereClause.userId = userId;
        }
        if (stageId) {
            whereClause.courseOutline = {
                stageId: stageId,
            };
        }
        const lessonProgress = await db_1.default.lessonProgress.findMany({
            where: whereClause,
            include: {
                user: {
                    select: {
                        id: true,
                        fullName: true,
                        phone: true,
                    },
                },
                courseOutline: {
                    include: {
                        stage: {
                            select: {
                                id: true,
                                title: true,
                                order: true,
                            },
                        },
                    },
                },
            },
            orderBy: [
                { courseOutline: { stage: { order: 'asc' } } },
                { courseOutline: { order: 'asc' } },
                { updatedAt: 'desc' },
            ],
        });
        const progressByUser = lessonProgress.reduce((acc, progress) => {
            const userId = progress.user.id;
            const stageId = progress.courseOutline.stage.id;
            if (!acc[userId]) {
                acc[userId] = {
                    user: progress.user,
                    stages: {},
                };
            }
            if (!acc[userId].stages[stageId]) {
                acc[userId].stages[stageId] = {
                    stage: progress.courseOutline.stage,
                    lessons: [],
                };
            }
            acc[userId].stages[stageId].lessons.push({
                id: progress.id,
                courseOutline: {
                    id: progress.courseOutline.id,
                    title: progress.courseOutline.title,
                    order: progress.courseOutline.order,
                    duration: progress.courseOutline.duration,
                },
                isCompleted: progress.isCompleted,
                watchTime: progress.watchTime,
                progressPercentage: progress.totalDuration
                    ? Math.min(100, Math.round((progress.watchTime / progress.totalDuration) * 100))
                    : 0,
                completedAt: progress.completedAt,
            });
            return acc;
        }, {});
        return (0, response_utils_1.sendSuccess)(res, 'Admin lesson progress retrieved successfully', {
            progressByUser,
            totalRecords: lessonProgress.length,
        });
    }
    catch (error) {
        console.error('Get admin lesson progress error:', error);
        return (0, response_utils_1.sendError)(res, 'Failed to retrieve lesson progress');
    }
};
exports.getAdminLessonProgress = getAdminLessonProgress;
//# sourceMappingURL=lesson.controller.js.map