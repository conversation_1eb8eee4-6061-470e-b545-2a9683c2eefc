export declare const hashPassword: (password: string) => Promise<string>;
export declare const comparePassword: (password: string, hash: string) => Promise<boolean>;
export declare const generateOTP: () => string;
export declare const generateUserCode: () => string;
export declare const normalizePhoneNumber: (phone: string) => string;
export declare const calculateQuizScore: (userAnswers: Array<{
    questionId: string;
    selectedAnswers: number[];
}>, correctAnswers: Array<{
    questionId: string;
    correctAnswers: number[];
}>) => number;
//# sourceMappingURL=auth.utils.d.ts.map